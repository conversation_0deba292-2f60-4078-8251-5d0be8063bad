# ninja log v5
1	4262	7758253798911014	CMakeFiles/rnscreens.dir/D_/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/cpp/RNSScreenRemovalListener.cpp.o	b97ed83dc2ec7d3d
699	4268	7758253798900673	CMakeFiles/rnscreens.dir/D_/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/cpp/RNScreensTurboModule.cpp.o	c3b7562d4ce98f89
1603	4270	7758253807950155	CMakeFiles/rnscreens.dir/src/main/cpp/jni-adapter.cpp.o	ce26243658fed6c9
3328	6118	7758253834161735	CMakeFiles/rnscreens.dir/src/main/cpp/OnLoad.cpp.o	e3c636b327d28289
2432	6276	7758253835697538	CMakeFiles/rnscreens.dir/src/main/cpp/NativeProxy.cpp.o	37e5ca4a9004bd0
6277	8280	7758253855805464	../../../../build/intermediates/cxx/Debug/1814394v/obj/armeabi-v7a/librnscreens.so	7f3d6f819879e545
