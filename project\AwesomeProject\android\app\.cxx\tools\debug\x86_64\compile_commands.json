[{"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dappmodules_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -ID:/ReactNative/A/project/AwesomeProject/android/app/build/generated/autolinking/src/main/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\D_\\ReactNative\\A\\project\\AwesomeProject\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dappmodules_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -ID:/ReactNative/A/project/AwesomeProject/android/app/build/generated/autolinking/src/main/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\OnLoad.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\Props.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\States.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\rnasyncstorage-generated.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\ComponentDescriptors.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\rngesturehandler_codegen-generated.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\rngesturehandler_codegen-generated.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\rngesturehandler_codegen-generated.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\Props.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\Props.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\Props.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\States.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\States.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\States.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\rnreanimated-generated.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\rnreanimated-generated.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\rnreanimated-generated.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\36cc7e4f7c143e028a6fb288618cc92c\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\36cc7e4f7c143e028a6fb288618cc92c\\components\\safeareacontext\\RNCSafeAreaViewState.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\7ab3dbfc414b53a74a6fbcd6c6024202\\components\\safeareacontext\\ComponentDescriptors.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\fb11c82c140a16fe66607aba37bca469\\renderer\\components\\safeareacontext\\EventEmitters.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\3102b4cb73674bba3518bf6e7ebbd7ca\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\fb11c82c140a16fe66607aba37bca469\\renderer\\components\\safeareacontext\\ShadowNodes.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\3102b4cb73674bba3518bf6e7ebbd7ca\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\fd44bf2af17c9885e2b06855f1a8570a\\safeareacontext\\safeareacontextJSI-generated.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\5471374b40316f0ce24d4ce738b43268\\source\\codegen\\jni\\safeareacontext-generated.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\7170110d2aaf1821d5c8a5f63553fd5f\\cpp\\react\\renderer\\components\\rnscreens\\RNSBottomTabsShadowNode.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSBottomTabsShadowNode.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSBottomTabsShadowNode.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\7170110d2aaf1821d5c8a5f63553fd5f\\cpp\\react\\renderer\\components\\rnscreens\\RNSBottomTabsState.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSBottomTabsState.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSBottomTabsState.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\4eaf070e219489ee5eaa04a54f5f8a28\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\7170110d2aaf1821d5c8a5f63553fd5f\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\7170110d2aaf1821d5c8a5f63553fd5f\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\21c2ed0500c30c63a9d93443faf63505\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\4eaf070e219489ee5eaa04a54f5f8a28\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\21c2ed0500c30c63a9d93443faf63505\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\4eaf070e219489ee5eaa04a54f5f8a28\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\b7575ff1969891f9a2f254655683fb9d\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\1e007443f7b23950854c7b4c739c763b\\react\\renderer\\components\\rnscreens\\RNSSplitViewScreenShadowNode.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSSplitViewScreenShadowNode.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSSplitViewScreenShadowNode.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\rnscreens.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\1417aff1ef66954b0563e2d55a615f9d\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\239ae99fed4a43719bf832c3deeb3aa8\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\b8c09388ff72cfeaefa92c766539e848\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\239ae99fed4a43719bf832c3deeb3aa8\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\b8c09388ff72cfeaefa92c766539e848\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xue<PERSON>\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\1417aff1ef66954b0563e2d55a615f9d\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\RNCWebViewSpec-generated.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\RNCWebViewSpec-generated.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\RNCWebViewSpec-generated.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\ComponentDescriptors.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ComponentDescriptors.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ComponentDescriptors.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\EventEmitters.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\EventEmitters.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\EventEmitters.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\Props.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\Props.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\Props.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\RNCWebViewSpecJSI-generated.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\RNCWebViewSpecJSI-generated.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\RNCWebViewSpecJSI-generated.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\ShadowNodes.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ShadowNodes.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ShadowNodes.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\States.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\States.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\States.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnworklets_autolinked_build\\CMakeFiles\\react_codegen_rnworklets.dir\\react\\renderer\\components\\rnworklets\\ComponentDescriptors.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\ComponentDescriptors.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\ComponentDescriptors.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnworklets_autolinked_build\\CMakeFiles\\react_codegen_rnworklets.dir\\react\\renderer\\components\\rnworklets\\EventEmitters.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\EventEmitters.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\EventEmitters.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnworklets_autolinked_build\\CMakeFiles\\react_codegen_rnworklets.dir\\react\\renderer\\components\\rnworklets\\Props.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\Props.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\Props.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnworklets_autolinked_build\\CMakeFiles\\react_codegen_rnworklets.dir\\react\\renderer\\components\\rnworklets\\ShadowNodes.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\ShadowNodes.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\ShadowNodes.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnworklets_autolinked_build\\CMakeFiles\\react_codegen_rnworklets.dir\\react\\renderer\\components\\rnworklets\\States.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\States.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\States.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnworklets_autolinked_build\\CMakeFiles\\react_codegen_rnworklets.dir\\react\\renderer\\components\\rnworklets\\rnworkletsJSI-generated.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\rnworkletsJSI-generated.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\rnworkletsJSI-generated.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=x86_64-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnworklets_autolinked_build\\CMakeFiles\\react_codegen_rnworklets.dir\\rnworklets-generated.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\rnworklets-generated.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\rnworklets-generated.cpp"}]