# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: GestureHandler
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/.cxx/Debug/r35181e2/armeabi-v7a/
# =============================================================================
# Object build statements for SHARED_LIBRARY target gesturehandler


#############################################
# Order-only phony target for gesturehandler

build cmake_object_order_depends_target_gesturehandler: phony || CMakeFiles/gesturehandler.dir

build CMakeFiles/gesturehandler.dir/cpp-adapter.cpp.o: CXX_COMPILER__gesturehandler_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/src/main/jni/cpp-adapter.cpp || cmake_object_order_depends_target_gesturehandler
  DEFINES = -Dgesturehandler_EXPORTS
  DEP_FILE = CMakeFiles\gesturehandler.dir\cpp-adapter.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -O2 -frtti -fexceptions -Wall -Werror -std=c++20 -DANDROID -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native/ReactCommon -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = CMakeFiles\gesturehandler.dir
  OBJECT_FILE_DIR = CMakeFiles\gesturehandler.dir
  TARGET_COMPILE_PDB = CMakeFiles\gesturehandler.dir\
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\node_modules\react-native-gesture-handler\android\build\intermediates\cxx\Debug\r35181e2\obj\armeabi-v7a\libgesturehandler.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target gesturehandler


#############################################
# Link the shared library D:\ReactNative\A\project\AwesomeProject\node_modules\react-native-gesture-handler\android\build\intermediates\cxx\Debug\r35181e2\obj\armeabi-v7a\libgesturehandler.so

build D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/intermediates/cxx/Debug/r35181e2/obj/armeabi-v7a/libgesturehandler.so: CXX_SHARED_LIBRARY_LINKER__gesturehandler_Debug CMakeFiles/gesturehandler.dir/cpp-adapter.cpp.o | D$:/xuexi/Android$ Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so D$:/xuexi/Android$ Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so D$:/xuexi/Android$ Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security  -O2 -frtti -fexceptions -Wall -Werror -std=c++20 -DANDROID -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments -Wl,--build-id=none
  LINK_LIBRARIES = "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so"  "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so"  "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so"  -latomic -lm
  OBJECT_DIR = CMakeFiles\gesturehandler.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libgesturehandler.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = CMakeFiles\gesturehandler.dir\
  TARGET_FILE = D:\ReactNative\A\project\AwesomeProject\node_modules\react-native-gesture-handler\android\build\intermediates\cxx\Debug\r35181e2\obj\armeabi-v7a\libgesturehandler.so
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\node_modules\react-native-gesture-handler\android\build\intermediates\cxx\Debug\r35181e2\obj\armeabi-v7a\libgesturehandler.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\ReactNative\A\project\AwesomeProject\node_modules\react-native-gesture-handler\android\.cxx\Debug\r35181e2\armeabi-v7a && "D:\xuexi\Android Sdk\Sdk\cmake\3.22.1\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\ReactNative\A\project\AwesomeProject\node_modules\react-native-gesture-handler\android\.cxx\Debug\r35181e2\armeabi-v7a && "D:\xuexi\Android Sdk\Sdk\cmake\3.22.1\bin\cmake.exe" --regenerate-during-build -SD:\ReactNative\A\project\AwesomeProject\node_modules\react-native-gesture-handler\android\src\main\jni -BD:\ReactNative\A\project\AwesomeProject\node_modules\react-native-gesture-handler\android\.cxx\Debug\r35181e2\armeabi-v7a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build gesturehandler: phony D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/intermediates/cxx/Debug/r35181e2/obj/armeabi-v7a/libgesturehandler.so

build libgesturehandler.so: phony D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/intermediates/cxx/Debug/r35181e2/obj/armeabi-v7a/libgesturehandler.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/.cxx/Debug/r35181e2/armeabi-v7a

build all: phony D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/intermediates/cxx/Debug/r35181e2/obj/armeabi-v7a/libgesturehandler.so

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/.cxx/Debug/r35181e2/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfig.cmake D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/.cxx/Debug/r35181e2/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/.cxx/Debug/r35181e2/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfig.cmake D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/.cxx/Debug/r35181e2/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfigVersion.cmake D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/src/main/jni/CMakeLists.txt D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/.cxx/Debug/r35181e2/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfig.cmake D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/.cxx/Debug/r35181e2/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/.cxx/Debug/r35181e2/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfig.cmake D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/.cxx/Debug/r35181e2/prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfigVersion.cmake D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/src/main/jni/CMakeLists.txt D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
