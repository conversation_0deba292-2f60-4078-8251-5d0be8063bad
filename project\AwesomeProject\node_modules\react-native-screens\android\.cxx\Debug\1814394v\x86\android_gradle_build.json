{"buildFiles": ["D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\1814394v\\prefab\\x86\\prefab\\lib\\i686-linux-android\\cmake\\fbjni\\fbjniConfig.cmake", "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\1814394v\\prefab\\x86\\prefab\\lib\\i686-linux-android\\cmake\\fbjni\\fbjniConfigVersion.cmake", "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\1814394v\\prefab\\x86\\prefab\\lib\\i686-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfig.cmake", "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\1814394v\\prefab\\x86\\prefab\\lib\\i686-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfigVersion.cmake", "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\xuexi\\Android Sdk\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\1814394v\\x86", "clean"]], "buildTargetsCommandComponents": ["D:\\xuexi\\Android Sdk\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\1814394v\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"rnscreens::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "x86", "artifactName": "rnscreens", "output": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\build\\intermediates\\cxx\\Debug\\1814394v\\obj\\x86\\librnscreens.so", "runtimeFiles": []}}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}