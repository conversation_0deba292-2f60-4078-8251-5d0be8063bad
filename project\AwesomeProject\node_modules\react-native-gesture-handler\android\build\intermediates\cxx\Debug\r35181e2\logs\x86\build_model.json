{"info": {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, "cxxBuildFolder": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\r35181e2\\x86", "soFolder": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\build\\intermediates\\cxx\\Debug\\r35181e2\\obj\\x86", "soRepublishFolder": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\build\\intermediates\\cmake\\debug\\obj\\x86", "abiPlatformVersion": 24, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": ["-DREACT_NATIVE_DIR=D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native", "-DREACT_NATIVE_MINOR_VERSION=80", "-DANDROID_STL=c++_shared", "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON", "-DCMAKE_SHARED_LINKER_FLAGS=-Wl,--build-id=none"], "cFlagsList": [], "cppFlagsList": ["-O2", "-frtti", "-fexceptions", "-Wall", "-Werror", "-std=c++20", "-DANDROID"], "variantName": "debug", "isDebuggableEnabled": true, "validAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\.cxx", "intermediatesBaseFolder": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\build\\intermediates", "intermediatesFolder": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\build\\intermediates\\cxx", "gradleModulePathName": ":react-native-gesture-handler", "moduleRootFolder": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android", "moduleBuildFile": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\build.gradle", "makeFile": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006", "ndkFolderBeforeSymLinking": "D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006", "ndkVersion": "27.1.12297006", "ndkSupportedAbiList": ["armeabi-v7a", "arm64-v8a", "riscv64", "x86", "x86_64"], "ndkDefaultAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 21, "max": 35, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33, "UpsideDownCake": 34, "VanillaIceCream": 35}}, "ndkMetaAbiList": [{"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, {"name": "riscv64", "bitness": 64, "isDefault": false, "isDeprecated": false, "architecture": "riscv64", "triple": "riscv64-linux-android", "llvmTriple": "riscv64-none-linux-android"}, {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, {"name": "x86_64", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "x86_64", "triple": "x86_64-linux-android", "llvmTriple": "x86_64-none-linux-android"}], "cmakeToolchainFile": "D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\build\\cmake\\android.toolchain.cmake", "cmake": {"cmakeExe": "D:\\xuexi\\Android Sdk\\Sdk\\cmake\\3.22.1\\bin\\cmake.exe"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"armeabi-v7a": "D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "arm64-v8a": "D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "riscv64": "D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\riscv64-linux-android\\libc++_shared.so", "x86": "D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "x86_64": "D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "D:\\ReactNative\\A\\project\\AwesomeProject\\android", "sdkFolder": "D:\\xuexi\\Android Sdk\\Sdk", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": true}, "outputOptions": [], "ninjaExe": "D:\\xuexi\\Android Sdk\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "hasBuildTimeInformation": true}, "prefabClassPaths": ["D:\\xuexi\\Android Sdk\\GradleCache\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar"], "prefabPackages": ["D:\\xuexi\\Android Sdk\\GradleCache\\caches\\8.14.1\\transforms\\5d0a6fceadea979a29a63bd98787fde9\\transformed\\react-android-0.80.2-debug\\prefab", "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\prefab_package\\debug\\prefab", "D:\\xuexi\\Android Sdk\\GradleCache\\caches\\8.14.1\\transforms\\51dbd9ae21c085b2cb843db84b5d6696\\transformed\\fbjni-0.7.0\\prefab"], "prefabPackageConfigurations": ["D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\prefab_package_configuration\\debug\\prefabDebugConfigurePackage\\prefab_publication.json"], "stlType": "c++_shared", "optimizationTag": "Debug"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\r35181e2\\prefab\\x86", "isActiveAbi": true, "fullConfigurationHash": "r35181e2r2io5i2v16p693k3g352h2b721t3m4b4n64112a3do4gnu43", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.9.2.\n#   - $NDK is the path to NDK 27.1.12297006.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-HD:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/src/main/jni\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=24\n-DANDROID_PLATFORM=android-24\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DCMAKE_ANDROID_NDK=$NDK\n-DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-DCMA<PERSON>_MAKE_PROGRAM=$NINJA\n-DCMAKE_CXX_FLAGS=-O2 -frtti -fexceptions -Wall -Werror -std=c++20 -DANDROID\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=Debug\n-DCMAKE_FIND_ROOT_PATH=D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/.cxx/Debug/$HASH/prefab/$ABI/prefab\n-BD:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/.cxx/Debug/$HASH/$ABI\n-GNinja\n-DREACT_NATIVE_DIR=D:/ReactNative/A/project/AwesomeProject/node_modules/react-native\n-DREACT_NATIVE_MINOR_VERSION=80\n-DANDROID_STL=c++_shared\n-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\n-DCMAKE_SHARED_LINKER_FLAGS=-Wl,--build-id=none", "configurationArguments": ["-HD:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=24", "-DANDROID_PLATFORM=android-24", "-DANDROID_ABI=x86", "-DCMAKE_ANDROID_ARCH_ABI=x86", "-DANDROID_NDK=D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006", "-DCMAKE_ANDROID_NDK=D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006", "-DCMAKE_TOOLCHAIN_FILE=D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=D:\\xuexi\\Android Sdk\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-DCMAKE_CXX_FLAGS=-O2 -frtti -fexceptions -Wall -Werror -std=c++20 -DANDROID", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\build\\intermediates\\cxx\\Debug\\r35181e2\\obj\\x86", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\build\\intermediates\\cxx\\Debug\\r35181e2\\obj\\x86", "-DCMAKE_BUILD_TYPE=Debug", "-DCMAKE_FIND_ROOT_PATH=D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\r35181e2\\prefab\\x86\\prefab", "-BD:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\r35181e2\\x86", "-<PERSON><PERSON><PERSON><PERSON>", "-DREACT_NATIVE_DIR=D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native", "-DREACT_NATIVE_MINOR_VERSION=80", "-DANDROID_STL=c++_shared", "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON", "-DCMAKE_SHARED_LINKER_FLAGS=-Wl,--build-id=none"], "stlLibraryFile": "D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "intermediatesParentFolder": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\build\\intermediates\\cxx\\Debug\\r35181e2"}