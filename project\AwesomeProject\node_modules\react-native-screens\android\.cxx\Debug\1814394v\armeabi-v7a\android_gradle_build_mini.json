{"buildFiles": ["D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\1814394v\\prefab\\armeabi-v7a\\prefab\\lib\\arm-linux-androideabi\\cmake\\fbjni\\fbjniConfig.cmake", "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\1814394v\\prefab\\armeabi-v7a\\prefab\\lib\\arm-linux-androideabi\\cmake\\fbjni\\fbjniConfigVersion.cmake", "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\1814394v\\prefab\\armeabi-v7a\\prefab\\lib\\arm-linux-androideabi\\cmake\\ReactAndroid\\ReactAndroidConfig.cmake", "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\1814394v\\prefab\\armeabi-v7a\\prefab\\lib\\arm-linux-androideabi\\cmake\\ReactAndroid\\ReactAndroidConfigVersion.cmake", "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\xuexi\\Android Sdk\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\1814394v\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["D:\\xuexi\\Android Sdk\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\1814394v\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"rnscreens::@6890427a1f51a3e7e1df": {"artifactName": "rnscreens", "abi": "armeabi-v7a", "output": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\build\\intermediates\\cxx\\Debug\\1814394v\\obj\\armeabi-v7a\\librnscreens.so", "runtimeFiles": []}}}