{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Gesture<PERSON>andler", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "gesturehandler::@6890427a1f51a3e7e1df", "jsonFile": "target-gesturehandler-Debug-905653877e1d68f7767b.json", "name": "gesturehandler", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/.cxx/Debug/r35181e2/x86", "source": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/src/main/jni"}, "version": {"major": 2, "minor": 3}}