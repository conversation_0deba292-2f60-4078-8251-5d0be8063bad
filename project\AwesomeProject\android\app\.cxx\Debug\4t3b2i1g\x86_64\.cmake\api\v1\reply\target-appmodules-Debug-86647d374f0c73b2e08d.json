{"artifacts": [{"path": "D:/ReactNative/A/project/AwesomeProject/android/app/build/intermediates/cxx/Debug/4t3b2i1g/obj/x86_64/libappmodules.so"}], "backtrace": 3, "backtraceGraph": {"commands": ["add_library", "include", "target_link_libraries", "target_compile_options", "target_compile_reactnative_options", "target_include_directories"], "files": ["D:/ReactNative/A/project/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake", "CMakeLists.txt", "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native/ReactCommon/cmake-utils/react-native-flags.cmake", "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 31, "parent": 0}, {"file": 0, "parent": 1}, {"command": 0, "file": 0, "line": 64, "parent": 2}, {"command": 2, "file": 0, "line": 95, "parent": 2}, {"command": 2, "file": 0, "line": 81, "parent": 2}, {"command": 4, "file": 0, "line": 71, "parent": 2}, {"command": 3, "file": 2, "line": 30, "parent": 6}, {"command": 3, "file": 2, "line": 36, "parent": 6}, {"command": 5, "file": 0, "line": 66, "parent": 2}, {"file": 3}, {"command": 5, "file": 3, "line": 83, "parent": 10}, {"file": 4}, {"command": 5, "file": 4, "line": 81, "parent": 12}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC"}, {"backtrace": 7, "fragment": "-Wall"}, {"backtrace": 7, "fragment": "-Werror"}, {"backtrace": 7, "fragment": "-fexceptions"}, {"backtrace": 7, "fragment": "-frtti"}, {"backtrace": 7, "fragment": "-std=c++20"}, {"backtrace": 7, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 8, "fragment": "-DLOG_TAG=\\\"ReactNative\\\""}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 4, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 4, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 4, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 4, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}], "defines": [{"define": "appmodules_EXPORTS"}], "includes": [{"backtrace": 9, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, {"backtrace": 9, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/build/generated/autolinking/src/main/jni"}, {"backtrace": 11, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni"}, {"backtrace": 13, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni"}, {"backtrace": 4, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage"}, {"backtrace": 4, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen"}, {"backtrace": 4, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated"}, {"backtrace": 4, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/."}, {"backtrace": 4, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 4, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext"}, {"backtrace": 4, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/."}, {"backtrace": 4, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 4, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 4, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens"}, {"backtrace": 4, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec"}, {"backtrace": 4, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/."}, {"backtrace": 4, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets"}, {"backtrace": 5, "isSystem": true, "path": "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"}, {"backtrace": 5, "isSystem": true, "path": "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include"}, {"backtrace": 5, "isSystem": true, "path": "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"}], "language": "CXX", "sourceIndexes": [0, 1], "sysroot": {"path": "D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "dependencies": [{"backtrace": 4, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe"}, {"backtrace": 4, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec"}, {"backtrace": 4, "id": "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a"}, {"backtrace": 4, "id": "react_codegen_rnworklets::@68f58d84d4754f193387"}, {"backtrace": 4, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a"}, {"backtrace": 4, "id": "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c"}, {"backtrace": 4, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad"}], "id": "appmodules::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments", "role": "flags"}, {"backtrace": 4, "fragment": "D:\\ReactNative\\A\\project\\AwesomeProject\\android\\app\\build\\intermediates\\cxx\\Debug\\4t3b2i1g\\obj\\x86_64\\libreact_codegen_safeareacontext.so", "role": "libraries"}, {"backtrace": 4, "fragment": "D:\\ReactNative\\A\\project\\AwesomeProject\\android\\app\\build\\intermediates\\cxx\\Debug\\4t3b2i1g\\obj\\x86_64\\libreact_codegen_rnscreens.so", "role": "libraries"}, {"backtrace": 5, "fragment": "\"D:\\xuexi\\Android Sdk\\GradleCache\\caches\\8.14.1\\transforms\\51dbd9ae21c085b2cb843db84b5d6696\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so\"", "role": "libraries"}, {"backtrace": 5, "fragment": "\"D:\\xuexi\\Android Sdk\\GradleCache\\caches\\8.14.1\\transforms\\5d0a6fceadea979a29a63bd98787fde9\\transformed\\react-android-0.80.2-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so\"", "role": "libraries"}, {"backtrace": 5, "fragment": "\"D:\\xuexi\\Android Sdk\\GradleCache\\caches\\8.14.1\\transforms\\5d0a6fceadea979a29a63bd98787fde9\\transformed\\react-android-0.80.2-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so\"", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "appmodules", "nameOnDisk": "libappmodules.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}, {"name": "Object Libraries", "sourceIndexes": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36]}], "sources": [{"backtrace": 3, "compileGroupIndex": 0, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "OnLoad.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/ComponentDescriptors.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/EventEmitters.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/Props.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/ShadowNodes.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/States.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/rnworkletsJSI-generated.cpp.o", "sourceGroupIndex": 1}, {"backtrace": 4, "isGenerated": true, "path": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/rnworklets-generated.cpp.o", "sourceGroupIndex": 1}], "type": "SHARED_LIBRARY"}