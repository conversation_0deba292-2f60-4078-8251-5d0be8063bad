# ninja log v5
1	3115	7758253460693935	CMakeFiles/rnscreens.dir/D_/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/cpp/RNScreensTurboModule.cpp.o	5e387ca112df07ce
676	3279	7758253476742776	CMakeFiles/rnscreens.dir/D_/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/cpp/RNSScreenRemovalListener.cpp.o	fad70b7be3625d79
1829	3616	7758253480113037	CMakeFiles/rnscreens.dir/src/main/cpp/jni-adapter.cpp.o	ba6c901f38db0782
2537	5050	7758253494485442	CMakeFiles/rnscreens.dir/src/main/cpp/OnLoad.cpp.o	60e65d0e61cc38a0
1126	5257	7758253496402348	CMakeFiles/rnscreens.dir/src/main/cpp/NativeProxy.cpp.o	d09ad1986412af42
5258	9710	7758253541102905	../../../../build/intermediates/cxx/Debug/1814394v/obj/arm64-v8a/librnscreens.so	bf92941afb3487cb
