{"buildFiles": ["D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\1814394v\\prefab\\x86_64\\prefab\\lib\\x86_64-linux-android\\cmake\\fbjni\\fbjniConfig.cmake", "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\1814394v\\prefab\\x86_64\\prefab\\lib\\x86_64-linux-android\\cmake\\fbjni\\fbjniConfigVersion.cmake", "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\1814394v\\prefab\\x86_64\\prefab\\lib\\x86_64-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfig.cmake", "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\1814394v\\prefab\\x86_64\\prefab\\lib\\x86_64-linux-android\\cmake\\ReactAndroid\\ReactAndroidConfigVersion.cmake", "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\xuexi\\Android Sdk\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\1814394v\\x86_64", "clean"]], "buildTargetsCommandComponents": ["D:\\xuexi\\Android Sdk\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\.cxx\\Debug\\1814394v\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"rnscreens::@6890427a1f51a3e7e1df": {"artifactName": "rnscreens", "abi": "x86_64", "output": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\build\\intermediates\\cxx\\Debug\\1814394v\\obj\\x86_64\\librnscreens.so", "runtimeFiles": []}}}