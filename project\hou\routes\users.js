var express = require('express');
var router = express.Router();
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { userModel } = require('../db/model');
const config = require('../config/config');

// 注册接口
router.post('/register', async (req, res) => {
  try {
    const { username, password, email, phone } = req.body;

    // 基础验证
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名和密码不能为空'
      });
    }

    if (password.length < 6) {
      return res.status(400).json({
        success: false,
        message: '密码至少6位'
      });
    }

    // 检查用户名是否已存在
    const existingUser = await userModel.findOne({ username });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '用户名已存在'
      });
    }

    // 检查邮箱是否已存在（如果提供了邮箱）
    if (email) {
      const existingEmail = await userModel.findOne({ email });
      if (existingEmail) {
        return res.status(400).json({
          success: false,
          message: '邮箱已被注册'
        });
      }
    }

    // 检查手机号是否已存在（如果提供了手机号）
    if (phone) {
      const existingPhone = await userModel.findOne({ phone });
      if (existingPhone) {
        return res.status(400).json({
          success: false,
          message: '手机号已被注册'
        });
      }
    }

    // 密码加密
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // 创建用户
    const newUser = new userModel({
      username,
      password: hashedPassword,
      email: email || undefined,
      phone: phone || undefined
    });

    await newUser.save();

    // 返回成功响应（不包含密码）
    const userResponse = {
      id: newUser._id,
      username: newUser.username,
      email: newUser.email,
      phone: newUser.phone,
      createdAt: newUser.createdAt
    };

    res.status(201).json({
      success: true,
      message: '注册成功',
      data: userResponse
    });

  } catch (error) {
    // {{ AURA-X: Modify - 移除调试日志. Approved: 代码质量修复. }}
    // 已移除调试日志
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 登录接口
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    // 基础验证
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名和密码不能为空'
      });
    }

    // 查找用户
    const user = await userModel.findOne({ username });
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    // {{ AURA-X: Modify - 修复用户状态检查. Approved: 数据一致性修复. }}
    // 检查用户状态
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: '账号已被禁用，请联系管理员'
      });
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    // 更新最后登录时间
    user.lastLoginTime = new Date();
    await user.save();

    // 生成JWT token
    const token = jwt.sign(
      {
        userId: user._id,
        username: user.username
      },
      config.jwt.secret,
      { expiresIn: '7d' }
    );

    // 返回成功响应
    const userResponse = {
      id: user._id,
      username: user.username,
      email: user.email,
      phone: user.phone,
      avatar: user.avatar,
      lastLoginTime: user.lastLoginTime
    };

    res.status(200).json({
      success: true,
      message: '登录成功',
      data: {
        user: userResponse,
        token: token
      }
    });

  } catch (error) {
    // 已移除调试日志
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 获取用户信息接口
router.get('/profile', async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({
        success: false,
        message: '请先登录'
      });
    }

    // 验证token
    const decoded = jwt.verify(token, config.jwt.secret);
    const user = await userModel.findById(decoded.userId).select('-password');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.status(200).json({
      success: true,
      data: user
    });

  } catch (error) {

    res.status(401).json({
      success: false,
      message: 'token无效'
    });
  }
});

/* GET users listing. */
router.get('/', function (req, res, next) {
  res.send('respond with a resource');
});

module.exports = router;
