{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/xuexi/Android Sdk/Sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/xuexi/Android Sdk/Sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/xuexi/Android Sdk/Sdk/cmake/3.22.1/bin/ctest.exe", "root": "D:/xuexi/Android Sdk/Sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-5f741f13e952e609b013.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-8aaf1248d202fd5806f1.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-5ff0eb6433df1905cbe8.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-8aaf1248d202fd5806f1.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-5ff0eb6433df1905cbe8.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-5f741f13e952e609b013.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}