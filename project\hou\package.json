{"name": "hou", "version": "0.0.0", "private": true, "scripts": {"start": "node ./bin/www", "dev": "nodemon ./bin/www"}, "dependencies": {"bcrypt": "^5.1.0", "cookie-parser": "~1.4.6", "cors": "^2.8.5", "debug": "~4.3.4", "dotenv": "^16.3.1", "express": "^4.18.2", "http-errors": "~2.0.0", "jade": "~1.11.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.17.0", "morgan": "~1.10.0", "socket.io": "^4.7.4"}, "devDependencies": {"nodemon": "^3.0.2"}}