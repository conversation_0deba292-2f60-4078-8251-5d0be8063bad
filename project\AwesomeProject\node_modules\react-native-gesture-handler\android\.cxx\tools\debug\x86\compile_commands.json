[{"directory": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/.cxx/Debug/r35181e2/x86", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=i686-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dgesturehandler_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native/ReactCommon -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -O2 -frtti -fexceptions -Wall -Werror -std=c++20 -DANDROID -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\gesturehandler.dir\\cpp-adapter.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\cpp-adapter.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\cpp-adapter.cpp"}]