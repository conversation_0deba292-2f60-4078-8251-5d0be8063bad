{"buildFiles": ["D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\xuexi\\Android Sdk\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\ReactNative\\A\\project\\AwesomeProject\\android\\app\\.cxx\\Debug\\4t3b2i1g\\x86_64", "clean"]], "buildTargetsCommandComponents": ["D:\\xuexi\\Android Sdk\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\ReactNative\\A\\project\\AwesomeProject\\android\\app\\.cxx\\Debug\\4t3b2i1g\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"appmodules::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "appmodules", "output": "D:\\ReactNative\\A\\project\\AwesomeProject\\android\\app\\build\\intermediates\\cxx\\Debug\\4t3b2i1g\\obj\\x86_64\\libappmodules.so", "runtimeFiles": ["D:\\ReactNative\\A\\project\\AwesomeProject\\android\\app\\build\\intermediates\\cxx\\Debug\\4t3b2i1g\\obj\\x86_64\\libreact_codegen_safeareacontext.so", "D:\\ReactNative\\A\\project\\AwesomeProject\\android\\app\\build\\intermediates\\cxx\\Debug\\4t3b2i1g\\obj\\x86_64\\libreact_codegen_rnscreens.so"]}, "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_RNCWebViewSpec"}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_rnasyncstorage"}, "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_rngesturehandler_codegen"}, "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_rnreanimated"}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_rnscreens", "output": "D:\\ReactNative\\A\\project\\AwesomeProject\\android\\app\\build\\intermediates\\cxx\\Debug\\4t3b2i1g\\obj\\x86_64\\libreact_codegen_rnscreens.so", "runtimeFiles": []}, "react_codegen_rnworklets::@68f58d84d4754f193387": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_rnworklets"}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_safeareacontext", "output": "D:\\ReactNative\\A\\project\\AwesomeProject\\android\\app\\build\\intermediates\\cxx\\Debug\\4t3b2i1g\\obj\\x86_64\\libreact_codegen_safeareacontext.so", "runtimeFiles": []}}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}