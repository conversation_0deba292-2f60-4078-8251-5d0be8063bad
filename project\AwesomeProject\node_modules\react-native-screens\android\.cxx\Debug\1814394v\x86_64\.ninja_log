# ninja log v5
0	8388	7758254439680523	CMakeFiles/rnscreens.dir/D_/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/cpp/RNScreensTurboModule.cpp.o	7a3d69216d52cfb4
906	8394	7758254459705532	CMakeFiles/rnscreens.dir/D_/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/cpp/RNSScreenRemovalListener.cpp.o	892f44dd1ee43ca
1603	8397	7758254502803268	CMakeFiles/rnscreens.dir/src/main/cpp/OnLoad.cpp.o	ab8858a9cad0df1d
7755	9334	7758254514204582	CMakeFiles/rnscreens.dir/src/main/cpp/jni-adapter.cpp.o	52a33a30fe8966b8
5974	10673	7758254527524660	CMakeFiles/rnscreens.dir/src/main/cpp/NativeProxy.cpp.o	2e6718bc2527d4d9
10673	12104	7758254541932925	../../../../build/intermediates/cxx/Debug/1814394v/obj/x86_64/librnscreens.so	be80d62d0351b5bf
