{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/xuexi/Android Sdk/Sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/xuexi/Android Sdk/Sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/xuexi/Android Sdk/Sdk/cmake/3.22.1/bin/ctest.exe", "root": "D:/xuexi/Android Sdk/Sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-ff1afb5bc01993fc1540.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-9af90c7c80f55dc97203.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-abec5c081b8402ff2450.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-9af90c7c80f55dc97203.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-abec5c081b8402ff2450.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-ff1afb5bc01993fc1540.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}