{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rnasyncstorage_autolinked_build", "jsonFile": "directory-rnasyncstorage_autolinked_build-Debug-5f186a8a80aa30b9a81e.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni", "targetIndexes": [2]}, {"build": "rngesturehandler_codegen_autolinked_build", "jsonFile": "directory-rngesturehandler_codegen_autolinked_build-Debug-aa84b81b2583405f2d4b.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni", "targetIndexes": [3]}, {"build": "rnreanimated_autolinked_build", "jsonFile": "directory-rnreanimated_autolinked_build-Debug-91ffd9d2724e59f2442b.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni", "targetIndexes": [4]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-Debug-e899951eb8a683217bb6.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [7]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-Debug-4c8d218048d535c518e7.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [5]}, {"build": "RNCWebViewSpec_autolinked_build", "jsonFile": "directory-RNCWebViewSpec_autolinked_build-Debug-0e773879a3cfa533f084.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni", "targetIndexes": [1]}, {"build": "rnworklets_autolinked_build", "jsonFile": "directory-rnworklets_autolinked_build-Debug-e2a86eeec3b307a0e175.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni", "targetIndexes": [6]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5, 6, 7], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-86647d374f0c73b2e08d.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 6, "id": "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c", "jsonFile": "target-react_codegen_RNCWebViewSpec-Debug-593b97f23af80a2affcf.json", "name": "react_codegen_RNCWebViewSpec", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe", "jsonFile": "target-react_codegen_rnasyncstorage-Debug-bdc89c97e4983472f0dc.json", "name": "react_codegen_rnasyncstorage", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec", "jsonFile": "target-react_codegen_rngesturehandler_codegen-Debug-4f8dfc703c49caadecc0.json", "name": "react_codegen_rngesturehandler_codegen", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a", "jsonFile": "target-react_codegen_rnreanimated-Debug-53229f9e8e3256d2b70b.json", "name": "react_codegen_rnreanimated", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-Debug-1837f235fda5a36d3332.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 7, "id": "react_codegen_rnworklets::@68f58d84d4754f193387", "jsonFile": "target-react_codegen_rnworklets-Debug-56fb7297d735e0eaa905.json", "name": "react_codegen_rnworklets", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-Debug-580c1adf3a5dfb81fb18.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64", "source": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}