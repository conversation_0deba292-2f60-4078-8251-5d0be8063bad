# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: rnscreens
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/.cxx/Debug/1814394v/x86_64/
# =============================================================================
# Object build statements for SHARED_LIBRARY target rnscreens


#############################################
# Order-only phony target for rnscreens

build cmake_object_order_depends_target_rnscreens: phony || CMakeFiles/rnscreens.dir

build CMakeFiles/rnscreens.dir/D_/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/cpp/RNScreensTurboModule.cpp.o: CXX_COMPILER__rnscreens_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/cpp/RNScreensTurboModule.cpp || cmake_object_order_depends_target_rnscreens
  DEFINES = -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS
  DEP_FILE = CMakeFiles\rnscreens.dir\D_\ReactNative\A\project\AwesomeProject\node_modules\react-native-screens\cpp\RNScreensTurboModule.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/../cpp -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = CMakeFiles\rnscreens.dir
  OBJECT_FILE_DIR = CMakeFiles\rnscreens.dir\D_\ReactNative\A\project\AwesomeProject\node_modules\react-native-screens\cpp
  TARGET_COMPILE_PDB = CMakeFiles\rnscreens.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\1814394v\obj\x86_64\librnscreens.pdb

build CMakeFiles/rnscreens.dir/D_/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/cpp/RNSScreenRemovalListener.cpp.o: CXX_COMPILER__rnscreens_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/cpp/RNSScreenRemovalListener.cpp || cmake_object_order_depends_target_rnscreens
  DEFINES = -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS
  DEP_FILE = CMakeFiles\rnscreens.dir\D_\ReactNative\A\project\AwesomeProject\node_modules\react-native-screens\cpp\RNSScreenRemovalListener.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/../cpp -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = CMakeFiles\rnscreens.dir
  OBJECT_FILE_DIR = CMakeFiles\rnscreens.dir\D_\ReactNative\A\project\AwesomeProject\node_modules\react-native-screens\cpp
  TARGET_COMPILE_PDB = CMakeFiles\rnscreens.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\1814394v\obj\x86_64\librnscreens.pdb

build CMakeFiles/rnscreens.dir/src/main/cpp/jni-adapter.cpp.o: CXX_COMPILER__rnscreens_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/cpp/jni-adapter.cpp || cmake_object_order_depends_target_rnscreens
  DEFINES = -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS
  DEP_FILE = CMakeFiles\rnscreens.dir\src\main\cpp\jni-adapter.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/../cpp -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = CMakeFiles\rnscreens.dir
  OBJECT_FILE_DIR = CMakeFiles\rnscreens.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\rnscreens.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\1814394v\obj\x86_64\librnscreens.pdb

build CMakeFiles/rnscreens.dir/src/main/cpp/NativeProxy.cpp.o: CXX_COMPILER__rnscreens_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/cpp/NativeProxy.cpp || cmake_object_order_depends_target_rnscreens
  DEFINES = -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS
  DEP_FILE = CMakeFiles\rnscreens.dir\src\main\cpp\NativeProxy.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/../cpp -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = CMakeFiles\rnscreens.dir
  OBJECT_FILE_DIR = CMakeFiles\rnscreens.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\rnscreens.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\1814394v\obj\x86_64\librnscreens.pdb

build CMakeFiles/rnscreens.dir/src/main/cpp/OnLoad.cpp.o: CXX_COMPILER__rnscreens_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/cpp/OnLoad.cpp || cmake_object_order_depends_target_rnscreens
  DEFINES = -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS
  DEP_FILE = CMakeFiles\rnscreens.dir\src\main\cpp\OnLoad.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/../cpp -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = CMakeFiles\rnscreens.dir
  OBJECT_FILE_DIR = CMakeFiles\rnscreens.dir\src\main\cpp
  TARGET_COMPILE_PDB = CMakeFiles\rnscreens.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\1814394v\obj\x86_64\librnscreens.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target rnscreens


#############################################
# Link the shared library ..\..\..\..\build\intermediates\cxx\Debug\1814394v\obj\x86_64\librnscreens.so

build ../../../../build/intermediates/cxx/Debug/1814394v/obj/x86_64/librnscreens.so: CXX_SHARED_LIBRARY_LINKER__rnscreens_Debug CMakeFiles/rnscreens.dir/D_/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/cpp/RNScreensTurboModule.cpp.o CMakeFiles/rnscreens.dir/D_/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/cpp/RNSScreenRemovalListener.cpp.o CMakeFiles/rnscreens.dir/src/main/cpp/jni-adapter.cpp.o CMakeFiles/rnscreens.dir/src/main/cpp/NativeProxy.cpp.o CMakeFiles/rnscreens.dir/src/main/cpp/OnLoad.cpp.o | D$:/xuexi/Android$ Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/libs/android.x86_64/libreactnative.so D$:/xuexi/Android$ Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/libs/android.x86_64/libjsi.so D$:/xuexi/Android$ Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86_64/libfbjni.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/libs/android.x86_64/libreactnative.so"  "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/libs/android.x86_64/libjsi.so"  "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86_64/libfbjni.so"  -landroid  -latomic -lm
  OBJECT_DIR = CMakeFiles\rnscreens.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = librnscreens.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = CMakeFiles\rnscreens.dir\
  TARGET_FILE = ..\..\..\..\build\intermediates\cxx\Debug\1814394v\obj\x86_64\librnscreens.so
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\1814394v\obj\x86_64\librnscreens.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\ReactNative\A\project\AwesomeProject\node_modules\react-native-screens\android\.cxx\Debug\1814394v\x86_64 && "D:\xuexi\Android Sdk\Sdk\cmake\3.22.1\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\ReactNative\A\project\AwesomeProject\node_modules\react-native-screens\android\.cxx\Debug\1814394v\x86_64 && "D:\xuexi\Android Sdk\Sdk\cmake\3.22.1\bin\cmake.exe" --regenerate-during-build -SD:\ReactNative\A\project\AwesomeProject\node_modules\react-native-screens\android -BD:\ReactNative\A\project\AwesomeProject\node_modules\react-native-screens\android\.cxx\Debug\1814394v\x86_64"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build librnscreens.so: phony ../../../../build/intermediates/cxx/Debug/1814394v/obj/x86_64/librnscreens.so

build rnscreens: phony ../../../../build/intermediates/cxx/Debug/1814394v/obj/x86_64/librnscreens.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/.cxx/Debug/1814394v/x86_64

build all: phony ../../../../build/intermediates/cxx/Debug/1814394v/obj/x86_64/librnscreens.so

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | ../../../../CMakeLists.txt ../prefab/x86_64/prefab/lib/x86_64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake ../prefab/x86_64/prefab/lib/x86_64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake ../prefab/x86_64/prefab/lib/x86_64-linux-android/cmake/fbjni/fbjniConfig.cmake ../prefab/x86_64/prefab/lib/x86_64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build ../../../../CMakeLists.txt ../prefab/x86_64/prefab/lib/x86_64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake ../prefab/x86_64/prefab/lib/x86_64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake ../prefab/x86_64/prefab/lib/x86_64-linux-android/cmake/fbjni/fbjniConfig.cmake ../prefab/x86_64/prefab/lib/x86_64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
