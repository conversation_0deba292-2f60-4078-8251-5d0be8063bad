{"artifacts": [{"path": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/intermediates/cxx/Debug/r35181e2/obj/arm64-v8a/libgesturehandler.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "add_compile_options", "target_include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 17, "parent": 0}, {"command": 1, "file": 0, "line": 31, "parent": 0}, {"command": 2, "file": 0, "line": 15, "parent": 0}, {"command": 3, "file": 0, "line": 22, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -O2 -frtti -fexceptions -Wall -Werror -std=c++20 -DANDROID -fno-limit-debug-info  -fPIC"}, {"backtrace": 3, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 3, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 3, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 3, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 3, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 3, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 3, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 3, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}], "defines": [{"define": "gesturehandler_EXPORTS"}], "includes": [{"backtrace": 4, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native/ReactCommon"}, {"backtrace": 2, "isSystem": true, "path": "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"}, {"backtrace": 2, "isSystem": true, "path": "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include"}, {"backtrace": 2, "isSystem": true, "path": "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "20"}, "sourceIndexes": [0], "sysroot": {"path": "D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "id": "gesturehandler::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments -Wl,--build-id=none", "role": "flags"}, {"backtrace": 2, "fragment": "\"D:\\xuexi\\Android Sdk\\GradleCache\\caches\\8.14.1\\transforms\\5d0a6fceadea979a29a63bd98787fde9\\transformed\\react-android-0.80.2-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\xuexi\\Android Sdk\\GradleCache\\caches\\8.14.1\\transforms\\5d0a6fceadea979a29a63bd98787fde9\\transformed\\react-android-0.80.2-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so\"", "role": "libraries"}, {"backtrace": 2, "fragment": "\"D:\\xuexi\\Android Sdk\\GradleCache\\caches\\8.14.1\\transforms\\51dbd9ae21c085b2cb843db84b5d6696\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so\"", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "gesturehandler", "nameOnDisk": "libgesturehandler.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "cpp-adapter.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}