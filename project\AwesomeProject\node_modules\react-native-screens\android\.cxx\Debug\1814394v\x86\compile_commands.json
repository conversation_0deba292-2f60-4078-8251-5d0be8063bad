[{"directory": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/.cxx/Debug/1814394v/x86", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=i686-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/../cpp -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o CMakeFiles\\rnscreens.dir\\D_\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\cpp\\RNScreensTurboModule.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\cpp\\RNScreensTurboModule.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\cpp\\RNScreensTurboModule.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/.cxx/Debug/1814394v/x86", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=i686-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/../cpp -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o CMakeFiles\\rnscreens.dir\\D_\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\cpp\\RNSScreenRemovalListener.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\cpp\\RNSScreenRemovalListener.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\cpp\\RNSScreenRemovalListener.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/.cxx/Debug/1814394v/x86", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=i686-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/../cpp -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o CMakeFiles\\rnscreens.dir\\src\\main\\cpp\\jni-adapter.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\src\\main\\cpp\\jni-adapter.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\src\\main\\cpp\\jni-adapter.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/.cxx/Debug/1814394v/x86", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=i686-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/../cpp -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o CMakeFiles\\rnscreens.dir\\src\\main\\cpp\\NativeProxy.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\src\\main\\cpp\\NativeProxy.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\src\\main\\cpp\\NativeProxy.cpp"}, {"directory": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/.cxx/Debug/1814394v/x86", "command": "\"D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe\" --target=i686-none-linux-android24 --sysroot=\"D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/../cpp -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include\" -isystem \"D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o CMakeFiles\\rnscreens.dir\\src\\main\\cpp\\OnLoad.cpp.o -c D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\src\\main\\cpp\\OnLoad.cpp", "file": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-screens\\android\\src\\main\\cpp\\OnLoad.cpp"}]