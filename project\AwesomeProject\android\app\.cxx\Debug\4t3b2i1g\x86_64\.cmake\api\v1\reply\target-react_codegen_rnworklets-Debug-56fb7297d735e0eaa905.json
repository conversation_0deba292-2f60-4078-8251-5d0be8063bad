{"artifacts": [{"path": "rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/./react/renderer/components/rnworklets/ComponentDescriptors.cpp.o"}, {"path": "rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/./react/renderer/components/rnworklets/EventEmitters.cpp.o"}, {"path": "rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/./react/renderer/components/rnworklets/Props.cpp.o"}, {"path": "rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/./react/renderer/components/rnworklets/ShadowNodes.cpp.o"}, {"path": "rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/./react/renderer/components/rnworklets/States.cpp.o"}, {"path": "rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/./react/renderer/components/rnworklets/rnworkletsJSI-generated.cpp.o"}, {"path": "rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/./rnworklets-generated.cpp.o"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_compile_options", "target_include_directories", "target_link_libraries"], "files": ["D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 11, "parent": 0}, {"command": 1, "file": 0, "line": 28, "parent": 0}, {"command": 2, "file": 0, "line": 17, "parent": 0}, {"command": 3, "file": 0, "line": 19, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC"}, {"backtrace": 2, "fragment": "-DLOG_TAG=\\\"ReactNative\\\""}, {"backtrace": 2, "fragment": "-fexceptions"}, {"backtrace": 2, "fragment": "-frtti"}, {"backtrace": 2, "fragment": "-std=c++20"}, {"backtrace": 2, "fragment": "-Wall"}, {"backtrace": 0, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 0, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 0, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 0, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}], "includes": [{"backtrace": 3, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/."}, {"backtrace": 3, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets"}, {"backtrace": 4, "isSystem": true, "path": "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"}, {"backtrace": 4, "isSystem": true, "path": "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include"}, {"backtrace": 4, "isSystem": true, "path": "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"}], "language": "CXX", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6], "sysroot": {"path": "D:/xuexi/Android Sdk/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "id": "react_codegen_rnworklets::@68f58d84d4754f193387", "name": "react_codegen_rnworklets", "paths": {"build": "rnworklets_autolinked_build", "source": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets/ComponentDescriptors.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets/EventEmitters.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets/Props.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets/ShadowNodes.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets/States.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets/rnworkletsJSI-generated.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/rnworklets-generated.cpp", "sourceGroupIndex": 0}], "type": "OBJECT_LIBRARY"}