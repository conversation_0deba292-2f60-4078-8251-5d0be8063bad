D:\ReactNative\A\project\AwesomeProject\node_modules\react-native-screens\android\.cxx\Debug\1814394v\prefab\x86\prefab\lib\i686-linux-android\cmake\fbjni\fbjniConfig.cmake
D:\ReactNative\A\project\AwesomeProject\node_modules\react-native-screens\android\.cxx\Debug\1814394v\prefab\x86\prefab\lib\i686-linux-android\cmake\fbjni\fbjniConfigVersion.cmake
D:\ReactNative\A\project\AwesomeProject\node_modules\react-native-screens\android\.cxx\Debug\1814394v\prefab\x86\prefab\lib\i686-linux-android\cmake\ReactAndroid\ReactAndroidConfig.cmake
D:\ReactNative\A\project\AwesomeProject\node_modules\react-native-screens\android\.cxx\Debug\1814394v\prefab\x86\prefab\lib\i686-linux-android\cmake\ReactAndroid\ReactAndroidConfigVersion.cmake
D:\ReactNative\A\project\AwesomeProject\node_modules\react-native-screens\android\CMakeLists.txt