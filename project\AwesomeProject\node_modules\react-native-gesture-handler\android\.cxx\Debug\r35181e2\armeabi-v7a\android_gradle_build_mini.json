{"buildFiles": ["D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\xuexi\\Android Sdk\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\r35181e2\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["D:\\xuexi\\Android Sdk\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\r35181e2\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"gesturehandler::@6890427a1f51a3e7e1df": {"artifactName": "gesturehandler", "abi": "armeabi-v7a", "output": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\build\\intermediates\\cxx\\Debug\\r35181e2\\obj\\armeabi-v7a\\libgesturehandler.so", "runtimeFiles": []}}}