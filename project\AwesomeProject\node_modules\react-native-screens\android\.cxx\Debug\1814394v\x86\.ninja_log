# ninja log v5
1	5632	7758254109596889	CMakeFiles/rnscreens.dir/D_/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/cpp/RNSScreenRemovalListener.cpp.o	1133b92187f1aed3
806	5639	7758254104596531	CMakeFiles/rnscreens.dir/D_/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/cpp/RNScreensTurboModule.cpp.o	803986a6e1abeb79
4427	5939	7758254127522441	CMakeFiles/rnscreens.dir/src/main/cpp/jni-adapter.cpp.o	78bb95e3b0ba7b3d
2659	7370	7758254141774963	CMakeFiles/rnscreens.dir/src/main/cpp/NativeProxy.cpp.o	7f3966597e71b98a
4944	7609	7758254144248947	CMakeFiles/rnscreens.dir/src/main/cpp/OnLoad.cpp.o	ab5ffd744b0de624
7609	9180	7758254159955567	../../../../build/intermediates/cxx/Debug/1814394v/obj/x86/librnscreens.so	e454c90a4cb00fe6
