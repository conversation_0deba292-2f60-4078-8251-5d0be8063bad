                        -HD:\ReactNative\A\project\AwesomeProject\node_modules\react-native-gesture-handler\android\src\main\jni
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=D:\xuexi\Android Sdk\Sdk\ndk\27.1.12297006
-DCMAKE_ANDROID_NDK=D:\xuexi\Android Sdk\Sdk\ndk\27.1.12297006
-DCMAKE_TOOLCHAIN_FILE=D:\xuexi\Android Sdk\Sdk\ndk\27.1.12297006\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\xuexi\Android Sdk\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_CXX_FLAGS=-O2 -frtti -fexceptions -Wall -Werror -std=c++20 -DANDROID
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\ReactNative\A\project\AwesomeProject\node_modules\react-native-gesture-handler\android\build\intermediates\cxx\Debug\r35181e2\obj\arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\ReactNative\A\project\AwesomeProject\node_modules\react-native-gesture-handler\android\build\intermediates\cxx\Debug\r35181e2\obj\arm64-v8a
-DCMAKE_BUILD_TYPE=Debug
-DCMAKE_FIND_ROOT_PATH=D:\ReactNative\A\project\AwesomeProject\node_modules\react-native-gesture-handler\android\.cxx\Debug\r35181e2\prefab\arm64-v8a\prefab
-BD:\ReactNative\A\project\AwesomeProject\node_modules\react-native-gesture-handler\android\.cxx\Debug\r35181e2\arm64-v8a
-GNinja
-DREACT_NATIVE_DIR=D:\ReactNative\A\project\AwesomeProject\node_modules\react-native
-DREACT_NATIVE_MINOR_VERSION=80
-DANDROID_STL=c++_shared
-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON
-DCMAKE_SHARED_LINKER_FLAGS=-Wl,--build-id=none
                        Build command args: []
                        Version: 2