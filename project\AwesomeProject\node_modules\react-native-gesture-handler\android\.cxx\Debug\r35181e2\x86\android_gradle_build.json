{"buildFiles": ["D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\xuexi\\Android Sdk\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\r35181e2\\x86", "clean"]], "buildTargetsCommandComponents": ["D:\\xuexi\\Android Sdk\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\r35181e2\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"gesturehandler::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "x86", "artifactName": "gesturehandler", "output": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\build\\intermediates\\cxx\\Debug\\r35181e2\\obj\\x86\\libgesturehandler.so", "runtimeFiles": []}}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\xuexi\\Android Sdk\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}