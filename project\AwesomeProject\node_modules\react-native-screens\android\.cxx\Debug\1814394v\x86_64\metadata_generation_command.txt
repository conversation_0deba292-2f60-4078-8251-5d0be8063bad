                        -HD:\ReactNative\A\project\AwesomeProject\node_modules\react-native-screens\android
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=D:\xuexi\Android Sdk\Sdk\ndk\27.1.12297006
-DCMAKE_ANDROID_NDK=D:\xuexi\Android Sdk\Sdk\ndk\27.1.12297006
-DCMAKE_TOOLCHAIN_FILE=D:\xuexi\Android Sdk\Sdk\ndk\27.1.12297006\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\xuexi\Android Sdk\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\ReactNative\A\project\AwesomeProject\node_modules\react-native-screens\android\build\intermediates\cxx\Debug\1814394v\obj\x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\ReactNative\A\project\AwesomeProject\node_modules\react-native-screens\android\build\intermediates\cxx\Debug\1814394v\obj\x86_64
-DCMAKE_BUILD_TYPE=Debug
-DCMAKE_FIND_ROOT_PATH=D:\ReactNative\A\project\AwesomeProject\node_modules\react-native-screens\android\.cxx\Debug\1814394v\prefab\x86_64\prefab
-BD:\ReactNative\A\project\AwesomeProject\node_modules\react-native-screens\android\.cxx\Debug\1814394v\x86_64
-GNinja
-DANDROID_STL=c++_shared
-DRNS_NEW_ARCH_ENABLED=true
-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON
                        Build command args: []
                        Version: 2