if(NOT TARGET react-native-reanimated::reanimated)
add_library(react-native-reanimated::reanimated SHARED IMPORTED)
set_target_properties(react-native-reanimated::reanimated PROPERTIES
    IMPORTED_LOCATION "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/intermediates/cxx/Debug/4h2q6r21/obj/x86/libreanimated.so"
    INTERFACE_INCLUDE_DIRECTORIES "D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/prefab-headers/reanimated"
    INTERFACE_LINK_LIBRARIES ""
)
endif()

