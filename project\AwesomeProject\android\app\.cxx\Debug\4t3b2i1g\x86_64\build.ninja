# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: appmodules
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.8


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = D$:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/
# =============================================================================
# Object build statements for SHARED_LIBRARY target appmodules


#############################################
# Order-only phony target for appmodules

build cmake_object_order_depends_target_appmodules: phony || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec cmake_object_order_depends_target_react_codegen_rnasyncstorage cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen cmake_object_order_depends_target_react_codegen_rnreanimated cmake_object_order_depends_target_react_codegen_rnscreens cmake_object_order_depends_target_react_codegen_rnworklets cmake_object_order_depends_target_react_codegen_safeareacontext

build CMakeFiles/appmodules.dir/D_/ReactNative/A/project/AwesomeProject/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o: CXX_COMPILER__appmodules_Debug D$:/ReactNative/A/project/AwesomeProject/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles\appmodules.dir\D_\ReactNative\A\project\AwesomeProject\android\app\build\generated\autolinking\src\main\jni\autolinking.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\"ReactNative\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -ID:/ReactNative/A/project/AwesomeProject/android/app/build/generated/autolinking/src/main/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles\appmodules.dir\D_\ReactNative\A\project\AwesomeProject\android\app\build\generated\autolinking\src\main\jni
  TARGET_COMPILE_PDB = CMakeFiles\appmodules.dir\
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libappmodules.pdb

build CMakeFiles/appmodules.dir/OnLoad.cpp.o: CXX_COMPILER__appmodules_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles\appmodules.dir\OnLoad.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -fexceptions -frtti -std=c++20 -DFOLLY_NO_CONFIG=1 -DLOG_TAG=\"ReactNative\" -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -ID:/ReactNative/A/project/AwesomeProject/android/app/build/generated/autolinking/src/main/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles\appmodules.dir
  TARGET_COMPILE_PDB = CMakeFiles\appmodules.dir\
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libappmodules.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target appmodules


#############################################
# Link the shared library D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libappmodules.so

build D$:/ReactNative/A/project/AwesomeProject/android/app/build/intermediates/cxx/Debug/4t3b2i1g/obj/x86_64/libappmodules.so: CXX_SHARED_LIBRARY_LINKER__appmodules_Debug rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/ComponentDescriptors.cpp.o rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/EventEmitters.cpp.o rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/Props.cpp.o rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/ShadowNodes.cpp.o rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/States.cpp.o rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/rnworkletsJSI-generated.cpp.o rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/rnworklets-generated.cpp.o CMakeFiles/appmodules.dir/D_/ReactNative/A/project/AwesomeProject/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o CMakeFiles/appmodules.dir/OnLoad.cpp.o | D$:/ReactNative/A/project/AwesomeProject/android/app/build/intermediates/cxx/Debug/4t3b2i1g/obj/x86_64/libreact_codegen_safeareacontext.so D$:/ReactNative/A/project/AwesomeProject/android/app/build/intermediates/cxx/Debug/4t3b2i1g/obj/x86_64/libreact_codegen_rnscreens.so D$:/xuexi/Android$ Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86_64/libfbjni.so D$:/xuexi/Android$ Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/libs/android.x86_64/libjsi.so D$:/xuexi/Android$ Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/libs/android.x86_64/libreactnative.so || D$:/ReactNative/A/project/AwesomeProject/android/app/build/intermediates/cxx/Debug/4t3b2i1g/obj/x86_64/libreact_codegen_rnscreens.so D$:/ReactNative/A/project/AwesomeProject/android/app/build/intermediates/cxx/Debug/4t3b2i1g/obj/x86_64/libreact_codegen_safeareacontext.so RNCWebViewSpec_autolinked_build/react_codegen_RNCWebViewSpec rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen rnreanimated_autolinked_build/react_codegen_rnreanimated rnworklets_autolinked_build/react_codegen_rnworklets
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = D:/ReactNative/A/project/AwesomeProject/android/app/build/intermediates/cxx/Debug/4t3b2i1g/obj/x86_64/libreact_codegen_safeareacontext.so  D:/ReactNative/A/project/AwesomeProject/android/app/build/intermediates/cxx/Debug/4t3b2i1g/obj/x86_64/libreact_codegen_rnscreens.so  "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86_64/libfbjni.so"  "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/libs/android.x86_64/libjsi.so"  "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/libs/android.x86_64/libreactnative.so"  -latomic -lm
  OBJECT_DIR = CMakeFiles\appmodules.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libappmodules.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = CMakeFiles\appmodules.dir\
  TARGET_FILE = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libappmodules.so
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libappmodules.pdb
  RSP_FILE = CMakeFiles\appmodules.rsp


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\ReactNative\A\project\AwesomeProject\android\app\.cxx\Debug\4t3b2i1g\x86_64 && "D:\xuexi\Android Sdk\Sdk\cmake\3.22.1\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\ReactNative\A\project\AwesomeProject\android\app\.cxx\Debug\4t3b2i1g\x86_64 && "D:\xuexi\Android Sdk\Sdk\cmake\3.22.1\bin\cmake.exe" --regenerate-during-build -SD:\ReactNative\A\project\AwesomeProject\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\ReactNative\A\project\AwesomeProject\android\app\.cxx\Debug\4t3b2i1g\x86_64"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/ReactNative/A/project/AwesomeProject/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnasyncstorage


#############################################
# Order-only phony target for react_codegen_rnasyncstorage

build cmake_object_order_depends_target_react_codegen_rnasyncstorage: phony || rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/Props.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/States.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\rnasyncstorageJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/rnasyncstorage-generated.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\rnasyncstorage-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""



#############################################
# Object library react_codegen_rnasyncstorage

build rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage: phony rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnasyncstorage_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\ReactNative\A\project\AwesomeProject\android\app\.cxx\Debug\4t3b2i1g\x86_64\rnasyncstorage_autolinked_build && "D:\xuexi\Android Sdk\Sdk\cmake\3.22.1\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnasyncstorage_autolinked_build/edit_cache: phony rnasyncstorage_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnasyncstorage_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\ReactNative\A\project\AwesomeProject\android\app\.cxx\Debug\4t3b2i1g\x86_64\rnasyncstorage_autolinked_build && "D:\xuexi\Android Sdk\Sdk\cmake\3.22.1\bin\cmake.exe" --regenerate-during-build -SD:\ReactNative\A\project\AwesomeProject\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\ReactNative\A\project\AwesomeProject\android\app\.cxx\Debug\4t3b2i1g\x86_64"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnasyncstorage_autolinked_build/rebuild_cache: phony rnasyncstorage_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/ReactNative/A/project/AwesomeProject/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rngesturehandler_codegen


#############################################
# Order-only phony target for react_codegen_rngesturehandler_codegen

build cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen: phony || rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/Props.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/States.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\rngesturehandler_codegenJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/rngesturehandler_codegen-generated.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\rngesturehandler_codegen-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\
  TARGET_PDB = ""



#############################################
# Object library react_codegen_rngesturehandler_codegen

build rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o


#############################################
# Utility command for edit_cache

build rngesturehandler_codegen_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\ReactNative\A\project\AwesomeProject\android\app\.cxx\Debug\4t3b2i1g\x86_64\rngesturehandler_codegen_autolinked_build && "D:\xuexi\Android Sdk\Sdk\cmake\3.22.1\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rngesturehandler_codegen_autolinked_build/edit_cache: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rngesturehandler_codegen_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\ReactNative\A\project\AwesomeProject\android\app\.cxx\Debug\4t3b2i1g\x86_64\rngesturehandler_codegen_autolinked_build && "D:\xuexi\Android Sdk\Sdk\cmake\3.22.1\bin\cmake.exe" --regenerate-during-build -SD:\ReactNative\A\project\AwesomeProject\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\ReactNative\A\project\AwesomeProject\android\app\.cxx\Debug\4t3b2i1g\x86_64"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rngesturehandler_codegen_autolinked_build/rebuild_cache: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/ReactNative/A/project/AwesomeProject/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnreanimated


#############################################
# Order-only phony target for react_codegen_rnreanimated

build cmake_object_order_depends_target_react_codegen_rnreanimated: phony || rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/Props.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/States.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\rnreanimatedJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/rnreanimated-generated.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\rnreanimated-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""



#############################################
# Object library react_codegen_rnreanimated

build rnreanimated_autolinked_build/react_codegen_rnreanimated: phony rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnreanimated_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\ReactNative\A\project\AwesomeProject\android\app\.cxx\Debug\4t3b2i1g\x86_64\rnreanimated_autolinked_build && "D:\xuexi\Android Sdk\Sdk\cmake\3.22.1\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnreanimated_autolinked_build/edit_cache: phony rnreanimated_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnreanimated_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\ReactNative\A\project\AwesomeProject\android\app\.cxx\Debug\4t3b2i1g\x86_64\rnreanimated_autolinked_build && "D:\xuexi\Android Sdk\Sdk\cmake\3.22.1\bin\cmake.exe" --regenerate-during-build -SD:\ReactNative\A\project\AwesomeProject\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\ReactNative\A\project\AwesomeProject\android\app\.cxx\Debug\4t3b2i1g\x86_64"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnreanimated_autolinked_build/rebuild_cache: phony rnreanimated_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/ReactNative/A/project/AwesomeProject/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Order-only phony target for react_codegen_safeareacontext

build cmake_object_order_depends_target_react_codegen_safeareacontext: phony || safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/36cc7e4f7c143e028a6fb288618cc92c/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\36cc7e4f7c143e028a6fb288618cc92c\components\safeareacontext\RNCSafeAreaViewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\36cc7e4f7c143e028a6fb288618cc92c\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/36cc7e4f7c143e028a6fb288618cc92c/components/safeareacontext/RNCSafeAreaViewState.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\36cc7e4f7c143e028a6fb288618cc92c\components\safeareacontext\RNCSafeAreaViewState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\36cc7e4f7c143e028a6fb288618cc92c\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7ab3dbfc414b53a74a6fbcd6c6024202/components/safeareacontext/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\7ab3dbfc414b53a74a6fbcd6c6024202\components\safeareacontext\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\7ab3dbfc414b53a74a6fbcd6c6024202\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/fb11c82c140a16fe66607aba37bca469/renderer/components/safeareacontext/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\fb11c82c140a16fe66607aba37bca469\renderer\components\safeareacontext\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\fb11c82c140a16fe66607aba37bca469\renderer\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3102b4cb73674bba3518bf6e7ebbd7ca/jni/react/renderer/components/safeareacontext/Props.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\3102b4cb73674bba3518bf6e7ebbd7ca\jni\react\renderer\components\safeareacontext\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\3102b4cb73674bba3518bf6e7ebbd7ca\jni\react\renderer\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/fb11c82c140a16fe66607aba37bca469/renderer/components/safeareacontext/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\fb11c82c140a16fe66607aba37bca469\renderer\components\safeareacontext\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\fb11c82c140a16fe66607aba37bca469\renderer\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3102b4cb73674bba3518bf6e7ebbd7ca/jni/react/renderer/components/safeareacontext/States.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\3102b4cb73674bba3518bf6e7ebbd7ca\jni\react\renderer\components\safeareacontext\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\3102b4cb73674bba3518bf6e7ebbd7ca\jni\react\renderer\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/fd44bf2af17c9885e2b06855f1a8570a/safeareacontext/safeareacontextJSI-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\fd44bf2af17c9885e2b06855f1a8570a\safeareacontext\safeareacontextJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\fd44bf2af17c9885e2b06855f1a8570a\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/5471374b40316f0ce24d4ce738b43268/source/codegen/jni/safeareacontext-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\5471374b40316f0ce24d4ce738b43268\source\codegen\jni\safeareacontext-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\5471374b40316f0ce24d4ce738b43268\source\codegen\jni
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_safeareacontext.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Link the shared library D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_safeareacontext.so

build D$:/ReactNative/A/project/AwesomeProject/android/app/build/intermediates/cxx/Debug/4t3b2i1g/obj/x86_64/libreact_codegen_safeareacontext.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_safeareacontext_Debug safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/36cc7e4f7c143e028a6fb288618cc92c/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/36cc7e4f7c143e028a6fb288618cc92c/components/safeareacontext/RNCSafeAreaViewState.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7ab3dbfc414b53a74a6fbcd6c6024202/components/safeareacontext/ComponentDescriptors.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/fb11c82c140a16fe66607aba37bca469/renderer/components/safeareacontext/EventEmitters.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3102b4cb73674bba3518bf6e7ebbd7ca/jni/react/renderer/components/safeareacontext/Props.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/fb11c82c140a16fe66607aba37bca469/renderer/components/safeareacontext/ShadowNodes.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3102b4cb73674bba3518bf6e7ebbd7ca/jni/react/renderer/components/safeareacontext/States.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/fd44bf2af17c9885e2b06855f1a8570a/safeareacontext/safeareacontextJSI-generated.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/5471374b40316f0ce24d4ce738b43268/source/codegen/jni/safeareacontext-generated.cpp.o | D$:/xuexi/Android$ Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86_64/libfbjni.so D$:/xuexi/Android$ Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/libs/android.x86_64/libjsi.so D$:/xuexi/Android$ Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/libs/android.x86_64/libreactnative.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86_64/libfbjni.so"  "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/libs/android.x86_64/libjsi.so"  "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/libs/android.x86_64/libreactnative.so"  -latomic -lm
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_safeareacontext.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_FILE = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_safeareacontext.so
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_safeareacontext.pdb


#############################################
# Utility command for edit_cache

build safeareacontext_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\ReactNative\A\project\AwesomeProject\android\app\.cxx\Debug\4t3b2i1g\x86_64\safeareacontext_autolinked_build && "D:\xuexi\Android Sdk\Sdk\cmake\3.22.1\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build safeareacontext_autolinked_build/edit_cache: phony safeareacontext_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\ReactNative\A\project\AwesomeProject\android\app\.cxx\Debug\4t3b2i1g\x86_64\safeareacontext_autolinked_build && "D:\xuexi\Android Sdk\Sdk\cmake\3.22.1\bin\cmake.exe" --regenerate-during-build -SD:\ReactNative\A\project\AwesomeProject\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\ReactNative\A\project\AwesomeProject\android\app\.cxx\Debug\4t3b2i1g\x86_64"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build safeareacontext_autolinked_build/rebuild_cache: phony safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/ReactNative/A/project/AwesomeProject/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_rnscreens


#############################################
# Order-only phony target for react_codegen_rnscreens

build cmake_object_order_depends_target_react_codegen_rnscreens: phony || rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/7170110d2aaf1821d5c8a5f63553fd5f/cpp/react/renderer/components/rnscreens/RNSBottomTabsShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSBottomTabsShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\7170110d2aaf1821d5c8a5f63553fd5f\cpp\react\renderer\components\rnscreens\RNSBottomTabsShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\7170110d2aaf1821d5c8a5f63553fd5f\cpp\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/7170110d2aaf1821d5c8a5f63553fd5f/cpp/react/renderer/components/rnscreens/RNSBottomTabsState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSBottomTabsState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\7170110d2aaf1821d5c8a5f63553fd5f\cpp\react\renderer\components\rnscreens\RNSBottomTabsState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\7170110d2aaf1821d5c8a5f63553fd5f\cpp\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/4eaf070e219489ee5eaa04a54f5f8a28/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\4eaf070e219489ee5eaa04a54f5f8a28\renderer\components\rnscreens\RNSFullWindowOverlayShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\4eaf070e219489ee5eaa04a54f5f8a28\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/7170110d2aaf1821d5c8a5f63553fd5f/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\7170110d2aaf1821d5c8a5f63553fd5f\cpp\react\renderer\components\rnscreens\RNSModalScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\7170110d2aaf1821d5c8a5f63553fd5f\cpp\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/7170110d2aaf1821d5c8a5f63553fd5f/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\7170110d2aaf1821d5c8a5f63553fd5f\cpp\react\renderer\components\rnscreens\RNSScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\7170110d2aaf1821d5c8a5f63553fd5f\cpp\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/21c2ed0500c30c63a9d93443faf63505/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\21c2ed0500c30c63a9d93443faf63505\components\rnscreens\RNSScreenStackHeaderConfigShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\21c2ed0500c30c63a9d93443faf63505\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/4eaf070e219489ee5eaa04a54f5f8a28/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\4eaf070e219489ee5eaa04a54f5f8a28\renderer\components\rnscreens\RNSScreenStackHeaderConfigState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\4eaf070e219489ee5eaa04a54f5f8a28\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/21c2ed0500c30c63a9d93443faf63505/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\21c2ed0500c30c63a9d93443faf63505\components\rnscreens\RNSScreenStackHeaderSubviewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\21c2ed0500c30c63a9d93443faf63505\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/4eaf070e219489ee5eaa04a54f5f8a28/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\4eaf070e219489ee5eaa04a54f5f8a28\renderer\components\rnscreens\RNSScreenStackHeaderSubviewState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\4eaf070e219489ee5eaa04a54f5f8a28\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b7575ff1969891f9a2f254655683fb9d/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\b7575ff1969891f9a2f254655683fb9d\common\cpp\react\renderer\components\rnscreens\RNSScreenState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\b7575ff1969891f9a2f254655683fb9d\common\cpp\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/1e007443f7b23950854c7b4c739c763b/react/renderer/components/rnscreens/RNSSplitViewScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSSplitViewScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\1e007443f7b23950854c7b4c739c763b\react\renderer\components\rnscreens\RNSSplitViewScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\1e007443f7b23950854c7b4c739c763b\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\rnscreens.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/1417aff1ef66954b0563e2d55a615f9d/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\1417aff1ef66954b0563e2d55a615f9d\jni\react\renderer\components\rnscreens\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\1417aff1ef66954b0563e2d55a615f9d\jni\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/239ae99fed4a43719bf832c3deeb3aa8/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\239ae99fed4a43719bf832c3deeb3aa8\codegen\jni\react\renderer\components\rnscreens\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\239ae99fed4a43719bf832c3deeb3aa8\codegen\jni\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b8c09388ff72cfeaefa92c766539e848/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\b8c09388ff72cfeaefa92c766539e848\source\codegen\jni\react\renderer\components\rnscreens\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\b8c09388ff72cfeaefa92c766539e848\source\codegen\jni\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/239ae99fed4a43719bf832c3deeb3aa8/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\239ae99fed4a43719bf832c3deeb3aa8\codegen\jni\react\renderer\components\rnscreens\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\239ae99fed4a43719bf832c3deeb3aa8\codegen\jni\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b8c09388ff72cfeaefa92c766539e848/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\b8c09388ff72cfeaefa92c766539e848\source\codegen\jni\react\renderer\components\rnscreens\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\b8c09388ff72cfeaefa92c766539e848\source\codegen\jni\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/1417aff1ef66954b0563e2d55a615f9d/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\1417aff1ef66954b0563e2d55a615f9d\jni\react\renderer\components\rnscreens\rnscreensJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\1417aff1ef66954b0563e2d55a615f9d\jni\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_rnscreens.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_rnscreens


#############################################
# Link the shared library D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_rnscreens.so

build D$:/ReactNative/A/project/AwesomeProject/android/app/build/intermediates/cxx/Debug/4t3b2i1g/obj/x86_64/libreact_codegen_rnscreens.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_rnscreens_Debug rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/7170110d2aaf1821d5c8a5f63553fd5f/cpp/react/renderer/components/rnscreens/RNSBottomTabsShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/7170110d2aaf1821d5c8a5f63553fd5f/cpp/react/renderer/components/rnscreens/RNSBottomTabsState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/4eaf070e219489ee5eaa04a54f5f8a28/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/7170110d2aaf1821d5c8a5f63553fd5f/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/7170110d2aaf1821d5c8a5f63553fd5f/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/21c2ed0500c30c63a9d93443faf63505/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/4eaf070e219489ee5eaa04a54f5f8a28/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/21c2ed0500c30c63a9d93443faf63505/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/4eaf070e219489ee5eaa04a54f5f8a28/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b7575ff1969891f9a2f254655683fb9d/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/1e007443f7b23950854c7b4c739c763b/react/renderer/components/rnscreens/RNSSplitViewScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/1417aff1ef66954b0563e2d55a615f9d/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/239ae99fed4a43719bf832c3deeb3aa8/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b8c09388ff72cfeaefa92c766539e848/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/239ae99fed4a43719bf832c3deeb3aa8/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b8c09388ff72cfeaefa92c766539e848/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/1417aff1ef66954b0563e2d55a615f9d/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o | D$:/xuexi/Android$ Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/libs/android.x86_64/libreactnative.so D$:/xuexi/Android$ Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/libs/android.x86_64/libjsi.so D$:/xuexi/Android$ Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86_64/libfbjni.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/libs/android.x86_64/libreactnative.so"  "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/libs/android.x86_64/libjsi.so"  "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86_64/libfbjni.so"  -latomic -lm
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_rnscreens.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_FILE = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_rnscreens.so
  TARGET_PDB = D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_rnscreens.pdb


#############################################
# Utility command for edit_cache

build rnscreens_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\ReactNative\A\project\AwesomeProject\android\app\.cxx\Debug\4t3b2i1g\x86_64\rnscreens_autolinked_build && "D:\xuexi\Android Sdk\Sdk\cmake\3.22.1\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnscreens_autolinked_build/edit_cache: phony rnscreens_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnscreens_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\ReactNative\A\project\AwesomeProject\android\app\.cxx\Debug\4t3b2i1g\x86_64\rnscreens_autolinked_build && "D:\xuexi\Android Sdk\Sdk\cmake\3.22.1\bin\cmake.exe" --regenerate-during-build -SD:\ReactNative\A\project\AwesomeProject\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\ReactNative\A\project\AwesomeProject\android\app\.cxx\Debug\4t3b2i1g\x86_64"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnscreens_autolinked_build/rebuild_cache: phony rnscreens_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/ReactNative/A/project/AwesomeProject/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNCWebViewSpec


#############################################
# Order-only phony target for react_codegen_RNCWebViewSpec

build cmake_object_order_depends_target_react_codegen_RNCWebViewSpec: phony || RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/RNCWebViewSpec-generated.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\RNCWebViewSpec-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  TARGET_COMPILE_PDB = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\
  TARGET_PDB = ""

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec
  TARGET_COMPILE_PDB = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\
  TARGET_PDB = ""

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec
  TARGET_COMPILE_PDB = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\
  TARGET_PDB = ""

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/Props.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec
  TARGET_COMPILE_PDB = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\
  TARGET_PDB = ""

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\RNCWebViewSpecJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec
  TARGET_COMPILE_PDB = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\
  TARGET_PDB = ""

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec
  TARGET_COMPILE_PDB = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\
  TARGET_PDB = ""

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/States.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec
  TARGET_COMPILE_PDB = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\
  TARGET_PDB = ""



#############################################
# Object library react_codegen_RNCWebViewSpec

build RNCWebViewSpec_autolinked_build/react_codegen_RNCWebViewSpec: phony RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o


#############################################
# Utility command for edit_cache

build RNCWebViewSpec_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\ReactNative\A\project\AwesomeProject\android\app\.cxx\Debug\4t3b2i1g\x86_64\RNCWebViewSpec_autolinked_build && "D:\xuexi\Android Sdk\Sdk\cmake\3.22.1\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build RNCWebViewSpec_autolinked_build/edit_cache: phony RNCWebViewSpec_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNCWebViewSpec_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\ReactNative\A\project\AwesomeProject\android\app\.cxx\Debug\4t3b2i1g\x86_64\RNCWebViewSpec_autolinked_build && "D:\xuexi\Android Sdk\Sdk\cmake\3.22.1\bin\cmake.exe" --regenerate-during-build -SD:\ReactNative\A\project\AwesomeProject\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\ReactNative\A\project\AwesomeProject\android\app\.cxx\Debug\4t3b2i1g\x86_64"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNCWebViewSpec_autolinked_build/rebuild_cache: phony RNCWebViewSpec_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/ReactNative/A/project/AwesomeProject/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnworklets


#############################################
# Order-only phony target for react_codegen_rnworklets

build cmake_object_order_depends_target_react_codegen_rnworklets: phony || rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir

build rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnworklets_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnworklets
  DEP_FILE = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\react\renderer\components\rnworklets\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir
  OBJECT_FILE_DIR = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\react\renderer\components\rnworklets
  TARGET_COMPILE_PDB = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\
  TARGET_PDB = ""

build rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnworklets_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnworklets
  DEP_FILE = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\react\renderer\components\rnworklets\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir
  OBJECT_FILE_DIR = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\react\renderer\components\rnworklets
  TARGET_COMPILE_PDB = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\
  TARGET_PDB = ""

build rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/Props.cpp.o: CXX_COMPILER__react_codegen_rnworklets_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets/Props.cpp || cmake_object_order_depends_target_react_codegen_rnworklets
  DEP_FILE = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\react\renderer\components\rnworklets\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir
  OBJECT_FILE_DIR = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\react\renderer\components\rnworklets
  TARGET_COMPILE_PDB = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\
  TARGET_PDB = ""

build rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnworklets_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnworklets
  DEP_FILE = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\react\renderer\components\rnworklets\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir
  OBJECT_FILE_DIR = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\react\renderer\components\rnworklets
  TARGET_COMPILE_PDB = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\
  TARGET_PDB = ""

build rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/States.cpp.o: CXX_COMPILER__react_codegen_rnworklets_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets/States.cpp || cmake_object_order_depends_target_react_codegen_rnworklets
  DEP_FILE = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\react\renderer\components\rnworklets\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir
  OBJECT_FILE_DIR = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\react\renderer\components\rnworklets
  TARGET_COMPILE_PDB = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\
  TARGET_PDB = ""

build rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/rnworkletsJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnworklets_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets/rnworkletsJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnworklets
  DEP_FILE = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\react\renderer\components\rnworklets\rnworkletsJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir
  OBJECT_FILE_DIR = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\react\renderer\components\rnworklets
  TARGET_COMPILE_PDB = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\
  TARGET_PDB = ""

build rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/rnworklets-generated.cpp.o: CXX_COMPILER__react_codegen_rnworklets_Debug D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/rnworklets-generated.cpp || cmake_object_order_depends_target_react_codegen_rnworklets
  DEP_FILE = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\rnworklets-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/. -ID:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/51dbd9ae21c085b2cb843db84b5d6696/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/jsi/include" -isystem "D:/xuexi/Android Sdk/GradleCache/caches/8.14.1/transforms/5d0a6fceadea979a29a63bd98787fde9/transformed/react-android-0.80.2-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir
  OBJECT_FILE_DIR = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir
  TARGET_COMPILE_PDB = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\
  TARGET_PDB = ""



#############################################
# Object library react_codegen_rnworklets

build rnworklets_autolinked_build/react_codegen_rnworklets: phony rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/ComponentDescriptors.cpp.o rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/EventEmitters.cpp.o rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/Props.cpp.o rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/ShadowNodes.cpp.o rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/States.cpp.o rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/rnworkletsJSI-generated.cpp.o rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/rnworklets-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnworklets_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\ReactNative\A\project\AwesomeProject\android\app\.cxx\Debug\4t3b2i1g\x86_64\rnworklets_autolinked_build && "D:\xuexi\Android Sdk\Sdk\cmake\3.22.1\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnworklets_autolinked_build/edit_cache: phony rnworklets_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnworklets_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\ReactNative\A\project\AwesomeProject\android\app\.cxx\Debug\4t3b2i1g\x86_64\rnworklets_autolinked_build && "D:\xuexi\Android Sdk\Sdk\cmake\3.22.1\bin\cmake.exe" --regenerate-during-build -SD:\ReactNative\A\project\AwesomeProject\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\ReactNative\A\project\AwesomeProject\android\app\.cxx\Debug\4t3b2i1g\x86_64"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnworklets_autolinked_build/rebuild_cache: phony rnworklets_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build appmodules: phony D$:/ReactNative/A/project/AwesomeProject/android/app/build/intermediates/cxx/Debug/4t3b2i1g/obj/x86_64/libappmodules.so

build libappmodules.so: phony D$:/ReactNative/A/project/AwesomeProject/android/app/build/intermediates/cxx/Debug/4t3b2i1g/obj/x86_64/libappmodules.so

build libreact_codegen_rnscreens.so: phony D$:/ReactNative/A/project/AwesomeProject/android/app/build/intermediates/cxx/Debug/4t3b2i1g/obj/x86_64/libreact_codegen_rnscreens.so

build libreact_codegen_safeareacontext.so: phony D$:/ReactNative/A/project/AwesomeProject/android/app/build/intermediates/cxx/Debug/4t3b2i1g/obj/x86_64/libreact_codegen_safeareacontext.so

build react_codegen_RNCWebViewSpec: phony RNCWebViewSpec_autolinked_build/react_codegen_RNCWebViewSpec

build react_codegen_rnasyncstorage: phony rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage

build react_codegen_rngesturehandler_codegen: phony rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen

build react_codegen_rnreanimated: phony rnreanimated_autolinked_build/react_codegen_rnreanimated

build react_codegen_rnscreens: phony D$:/ReactNative/A/project/AwesomeProject/android/app/build/intermediates/cxx/Debug/4t3b2i1g/obj/x86_64/libreact_codegen_rnscreens.so

build react_codegen_rnworklets: phony rnworklets_autolinked_build/react_codegen_rnworklets

build react_codegen_safeareacontext: phony D$:/ReactNative/A/project/AwesomeProject/android/app/build/intermediates/cxx/Debug/4t3b2i1g/obj/x86_64/libreact_codegen_safeareacontext.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64

build all: phony D$:/ReactNative/A/project/AwesomeProject/android/app/build/intermediates/cxx/Debug/4t3b2i1g/obj/x86_64/libappmodules.so rnasyncstorage_autolinked_build/all rngesturehandler_codegen_autolinked_build/all rnreanimated_autolinked_build/all safeareacontext_autolinked_build/all rnscreens_autolinked_build/all RNCWebViewSpec_autolinked_build/all rnworklets_autolinked_build/all

# =============================================================================

#############################################
# Folder: D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/RNCWebViewSpec_autolinked_build

build RNCWebViewSpec_autolinked_build/all: phony RNCWebViewSpec_autolinked_build/react_codegen_RNCWebViewSpec

# =============================================================================

#############################################
# Folder: D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rnasyncstorage_autolinked_build

build rnasyncstorage_autolinked_build/all: phony rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage

# =============================================================================

#############################################
# Folder: D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rngesturehandler_codegen_autolinked_build

build rngesturehandler_codegen_autolinked_build/all: phony rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen

# =============================================================================

#############################################
# Folder: D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rnreanimated_autolinked_build

build rnreanimated_autolinked_build/all: phony rnreanimated_autolinked_build/react_codegen_rnreanimated

# =============================================================================

#############################################
# Folder: D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rnscreens_autolinked_build

build rnscreens_autolinked_build/all: phony D$:/ReactNative/A/project/AwesomeProject/android/app/build/intermediates/cxx/Debug/4t3b2i1g/obj/x86_64/libreact_codegen_rnscreens.so

# =============================================================================

#############################################
# Folder: D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/rnworklets_autolinked_build

build rnworklets_autolinked_build/all: phony rnworklets_autolinked_build/react_codegen_rnworklets

# =============================================================================

#############################################
# Folder: D:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/safeareacontext_autolinked_build

build safeareacontext_autolinked_build/all: phony D$:/ReactNative/A/project/AwesomeProject/android/app/build/intermediates/cxx/Debug/4t3b2i1g/obj/x86_64/libreact_codegen_safeareacontext.so

# =============================================================================
# Built-in targets


#############################################
# Phony target to force glob verification run.

build D$:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/CMakeFiles/VerifyGlobs.cmake_force: phony


#############################################
# Re-run CMake to check if globbed directories changed.

build D$:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/CMakeFiles/cmake.verify_globs: VERIFY_GLOBS | D$:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/CMakeFiles/VerifyGlobs.cmake_force
  pool = console
  restat = 1


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE D$:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/CMakeFiles/cmake.verify_globs | CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake D$:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/CMakeFiles/VerifyGlobs.cmake D$:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/prefab/x86_64/prefab/lib/x86_64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake D$:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/prefab/x86_64/prefab/lib/x86_64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake D$:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/prefab/x86_64/prefab/lib/x86_64-linux-android/cmake/fbjni/fbjniConfig.cmake D$:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/prefab/x86_64/prefab/lib/x86_64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake D$:/ReactNative/A/project/AwesomeProject/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake D$:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native/ReactCommon/cmake-utils/react-native-flags.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CheckIPOSupported.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CheckIPOSupported/CMakeLists-CXX.txt.in D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CheckIPOSupported/foo.cpp D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CheckIPOSupported/main.cpp D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake D$:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/x86_64/CMakeFiles/VerifyGlobs.cmake D$:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/prefab/x86_64/prefab/lib/x86_64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake D$:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/prefab/x86_64/prefab/lib/x86_64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake D$:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/prefab/x86_64/prefab/lib/x86_64-linux-android/cmake/fbjni/fbjniConfig.cmake D$:/ReactNative/A/project/AwesomeProject/android/app/.cxx/Debug/4t3b2i1g/prefab/x86_64/prefab/lib/x86_64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake D$:/ReactNative/A/project/AwesomeProject/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake D$:/ReactNative/A/project/AwesomeProject/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake D$:/ReactNative/A/project/AwesomeProject/node_modules/react-native/ReactCommon/cmake-utils/react-native-flags.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CheckIPOSupported.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CheckIPOSupported/CMakeLists-CXX.txt.in D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CheckIPOSupported/foo.cpp D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CheckIPOSupported/main.cpp D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake D$:/xuexi/Android$ Sdk/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake D$:/xuexi/Android$ Sdk/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
