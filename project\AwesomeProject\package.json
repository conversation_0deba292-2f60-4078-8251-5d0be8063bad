{"name": "AwesomeProject", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-native/new-app-screen": "0.80.2", "@react-navigation/bottom-tabs": "^7.4.4", "@react-navigation/native": "^7.1.16", "@react-navigation/stack": "^7.4.4", "axios": "^1.11.0", "react": "19.1.0", "react-native": "0.80.2", "react-native-gesture-handler": "^2.27.2", "react-native-reanimated": "^4.0.1", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.13.1", "react-native-webview": "^13.15.0", "react-native-snap-carousel": "^3.9.1", "react-native-worklets": "^0.4.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "19.1.1", "@react-native-community/cli-platform-android": "19.1.1", "@react-native-community/cli-platform-ios": "19.1.1", "@react-native/babel-preset": "0.80.2", "@react-native/eslint-config": "0.80.2", "@react-native/metro-config": "0.80.2", "@react-native/typescript-config": "0.80.2", "@types/jest": "^29.5.13", "@types/react": "^19.1.0", "@types/react-test-renderer": "^19.1.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^8.19.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.1.3", "jest": "^29.6.3", "prettier": "^3.6.2", "react-test-renderer": "19.1.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}