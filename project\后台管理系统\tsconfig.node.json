{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "allowSyntheticDefaultImports": true, "strict": true, "noEmit": true, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.node.tsbuildinfo", "skipLibCheck": true, "resolveJsonModule": true, "allowImportingTsExtensions": true, "isolatedModules": true, "moduleDetection": "force"}, "include": ["vite.config.*", "vitest.config.*", "cypress.config.*", "nightwatch.conf.*", "playwright.config.*", "eslint.config.*"]}