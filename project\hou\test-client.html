<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天功能测试客户端</title>
    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .status {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            background: rgba(255, 255, 255, 0.2);
        }

        .main-content {
            display: flex;
            height: 600px;
        }

        .sidebar {
            width: 300px;
            background: #f8f9fa;
            border-right: 1px solid #e9ecef;
            display: flex;
            flex-direction: column;
        }

        .user-info {
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            background: white;
        }

        .user-input {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }

        .user-input input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .user-input button {
            padding: 8px 15px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        .user-input button:hover {
            background: #5a6fd8;
        }

        .online-users {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .online-users h3 {
            margin-bottom: 15px;
            color: #333;
        }

        .user-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin-bottom: 8px;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .user-item:hover {
            background: #e3f2fd;
            transform: translateX(5px);
        }

        .user-item.active {
            background: #667eea;
            color: white;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #667eea;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: white;
            font-weight: bold;
        }

        .user-details {
            flex: 1;
        }

        .user-name {
            font-weight: 600;
            margin-bottom: 2px;
        }

        .user-role {
            font-size: 12px;
            opacity: 0.7;
        }

        .user-status {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4caf50;
        }

        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            background: white;
        }

        .chat-header h3 {
            color: #333;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message.sent {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            position: relative;
        }

        .message.received .message-content {
            background: white;
            border: 1px solid #e9ecef;
        }

        .message.sent .message-content {
            background: #667eea;
            color: white;
        }

        .message-info {
            font-size: 12px;
            opacity: 0.7;
            margin-top: 5px;
        }

        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }

        .chat-input-container {
            display: flex;
            gap: 10px;
        }

        .chat-input-container input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
        }

        .chat-input-container input:focus {
            border-color: #667eea;
        }

        .chat-input-container button {
            padding: 12px 20px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
        }

        .chat-input-container button:hover {
            background: #5a6fd8;
        }

        .typing-indicator {
            padding: 10px 20px;
            font-style: italic;
            color: #666;
            font-size: 14px;
        }

        .log-area {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }

        .log-info {
            background: rgba(59, 130, 246, 0.1);
            border-left: 3px solid #3b82f6;
        }

        .log-success {
            background: rgba(34, 197, 94, 0.1);
            border-left: 3px solid #22c55e;
        }

        .log-error {
            background: rgba(239, 68, 68, 0.1);
            border-left: 3px solid #ef4444;
        }

        .log-warning {
            background: rgba(245, 158, 11, 0.1);
            border-left: 3px solid #f59e0b;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>💬 聊天功能测试客户端</h1>
            <div class="status" id="connectionStatus">未连接</div>
        </div>

        <div class="main-content">
            <div class="sidebar">
                <div class="user-info">
                    <h3>👤 用户信息</h3>
                    <div class="user-input">
                        <input type="text" id="userId" placeholder="用户ID" value="user1">
                        <button onclick="joinUser()">加入</button>
                    </div>
                    <div class="user-input">
                        <input type="text" id="username" placeholder="用户名" value="测试用户1">
                        <button onclick="updateUser()">更新</button>
                    </div>
                </div>

                <div class="online-users">
                    <h3>👥 在线用户</h3>
                    <div id="onlineUsersList"></div>
                </div>
            </div>

            <div class="chat-area">
                <div class="chat-header">
                    <h3 id="chatTitle">💬 聊天区域</h3>
                </div>

                <div class="chat-messages" id="chatMessages">
                    <div class="message">
                        <div class="message-content">
                            <div>欢迎使用聊天功能测试客户端！</div>
                            <div class="message-info">系统消息</div>
                        </div>
                    </div>
                </div>

                <div class="typing-indicator" id="typingIndicator" style="display: none;">
                    对方正在输入...
                </div>

                <div class="chat-input">
                    <div class="chat-input-container">
                        <input type="text" id="messageInput" placeholder="输入消息..." onkeypress="handleKeyPress(event)">
                        <button onclick="sendMessage()">发送</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="log-area" id="logArea">
            <div class="log-entry log-info">系统启动，等待连接...</div>
        </div>
    </div>

    <script>
        let socket;
        let currentUser = null;
        let selectedUser = null;
        let currentSessionId = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function () {
            connectSocket();
        });

        // 连接WebSocket
        function connectSocket() {
            socket = io('http://localhost:3000');

            socket.on('connect', () => {
                log('WebSocket连接成功', 'success');
                updateConnectionStatus('已连接');
            });

            socket.on('disconnect', () => {
                log('WebSocket连接断开', 'error');
                updateConnectionStatus('连接断开');
            });

            socket.on('error', (error) => {
                log('WebSocket错误: ' + error.message, 'error');
            });

            // 用户加入成功
            socket.on('user:joined', (data) => {
                log('用户加入成功: ' + JSON.stringify(data.user), 'success');
                currentUser = data.user;
            });

            // 接收消息
            socket.on('message:receive', (data) => {
                log('收到消息: ' + data.message.content, 'info');
                addMessage(data.message, false);
            });

            // 消息发送成功
            socket.on('message:sent', (data) => {
                log('消息发送成功: ' + data.message.content, 'success');
                addMessage(data.message, true);
            });

            // 用户状态更新
            socket.on('user:status', (data) => {
                log('用户状态更新: ' + data.userId + ' -> ' + data.status, 'info');
                updateOnlineUsers();
            });

            // 在线用户列表
            socket.on('users:online', (users) => {
                log('在线用户列表更新: ' + users.length + ' 个用户', 'info');
                displayOnlineUsers(users);
            });

            // 聊天历史
            socket.on('chat:history', (data) => {
                log('收到聊天历史: ' + data.data.length + ' 条消息', 'info');
                displayChatHistory(data.data);
            });

            // 正在输入状态
            socket.on('typing:start', (data) => {
                showTypingIndicator(true);
            });

            socket.on('typing:stop', (data) => {
                showTypingIndicator(false);
            });
        }

        // 用户加入
        function joinUser() {
            const userId = document.getElementById('userId').value;
            const username = document.getElementById('username').value;

            if (!userId || !username) {
                log('请输入用户ID和用户名', 'warning');
                return;
            }

            socket.emit('user:join', {
                id: userId,
                username: username,
                realName: username,
                avatar: '',
                role: 'user'
            });

            log('正在加入聊天...', 'info');
        }

        // 更新用户信息
        function updateUser() {
            const username = document.getElementById('username').value;
            if (currentUser) {
                currentUser.username = username;
                log('用户信息已更新', 'success');
            }
        }

        // 发送消息
        function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const content = messageInput.value.trim();

            if (!content) {
                log('请输入消息内容', 'warning');
                return;
            }

            if (!currentUser || !selectedUser) {
                log('请先选择聊天对象', 'warning');
                return;
            }

            // 创建或获取会话
            createOrGetSession(currentUser.id, selectedUser.id).then(sessionId => {
                socket.emit('message:send', {
                    sessionId: sessionId,
                    senderId: currentUser.id,
                    receiverId: selectedUser.id,
                    content: content,
                    messageType: 'text'
                });

                messageInput.value = '';
            });
        }

        // 处理回车键
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // 创建或获取会话
        async function createOrGetSession(userId1, userId2) {
            if (currentSessionId) {
                return currentSessionId;
            }

            try {
                const response = await fetch('/api/chat/sessions/private', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        userId1: userId1,
                        userId2: userId2
                    })
                });

                const data = await response.json();
                if (data.success) {
                    currentSessionId = data.data._id;
                    return currentSessionId;
                }
            } catch (error) {
                log('创建会话失败: ' + error.message, 'error');
            }

            return null;
        }

        // 选择用户
        function selectUser(user) {
            selectedUser = user;
            currentSessionId = null; // 重置会话ID

            // 更新UI
            document.querySelectorAll('.user-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.closest('.user-item').classList.add('active');

            document.getElementById('chatTitle').textContent = `💬 与 ${user.realName || user.username} 聊天`;

            // 清空消息区域
            // {{ AURA-X: Modify - 使用textContent避免XSS风险. Approved: 安全修复. }}
            const messagesElement = document.getElementById('chatMessages');
            messagesElement.textContent = `
                <div class="message">
                    <div class="message-content">
                        <div>开始与 ${user.realName || user.username} 聊天</div>
                        <div class="message-info">系统消息</div>
                    </div>
                </div>
            `;

            log('选择聊天对象: ' + (user.realName || user.username), 'info');
        }

        // 显示在线用户
        function displayOnlineUsers(users) {
            const container = document.getElementById('onlineUsersList');
            // {{ AURA-X: Modify - 安全地清空容器内容. Approved: 安全修复. }}
            while (container.firstChild) {
                container.removeChild(container.firstChild);
            }

            users.forEach(user => {
                if (user.id === currentUser?.id) return; // 不显示自己

                const userItem = document.createElement('div');
                userItem.className = 'user-item';
                userItem.onclick = () => selectUser(user);

                // {{ AURA-X: Modify - 使用安全的DOM操作避免XSS. Approved: 安全修复. }}
                const userAvatar = document.createElement('div');
                userAvatar.className = 'user-avatar';
                userAvatar.textContent = (user.realName || user.username).charAt(0);

                const userDetails = document.createElement('div');
                userDetails.className = 'user-details';

                const userName = document.createElement('div');
                userName.className = 'user-name';
                userName.textContent = user.realName || user.username;

                const userRole = document.createElement('div');
                userRole.className = 'user-role';
                userRole.textContent = user.role || 'user';

                userDetails.appendChild(userName);
                userDetails.appendChild(userRole);

                const userStatus = document.createElement('div');
                userStatus.className = 'user-status';

                userItem.appendChild(userAvatar);
                userItem.appendChild(userDetails);
                userItem.appendChild(userStatus);

                container.appendChild(userItem);
            });
        }

        // 添加消息到聊天区域
        function addMessage(message, isSent) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isSent ? 'sent' : 'received'}`;

            const time = new Date(message.createdAt || Date.now()).toLocaleTimeString();

            // {{ AURA-X: Modify - 安全创建消息内容避免XSS. Approved: 安全修复. }}
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';

            const messageText = document.createElement('div');
            messageText.textContent = message.content;

            const messageInfo = document.createElement('div');
            messageInfo.className = 'message-info';
            messageInfo.textContent = `${time} - ${isSent ? '我' : (message.sender?.realName || message.sender?.username)}`;

            messageContent.appendChild(messageText);
            messageContent.appendChild(messageInfo);
            messageDiv.appendChild(messageContent);

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // 显示聊天历史
        function displayChatHistory(messages) {
            const messagesContainer = document.getElementById('chatMessages');
            // {{ AURA-X: Modify - 安全清空消息容器. Approved: 安全修复. }}
            while (messagesContainer.firstChild) {
                messagesContainer.removeChild(messagesContainer.firstChild);
            }

            messages.forEach(message => {
                const isSent = message.sender?.id === currentUser?.id;
                addMessage(message, isSent);
            });
        }

        // 显示正在输入指示器
        function showTypingIndicator(show) {
            const indicator = document.getElementById('typingIndicator');
            indicator.style.display = show ? 'block' : 'none';
        }

        // 更新连接状态
        function updateConnectionStatus(status) {
            document.getElementById('connectionStatus').textContent = status;
        }

        // 更新在线用户列表
        function updateOnlineUsers() {
            socket.emit('users:online');
        }

        // 日志记录
        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;

            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;
        }

        // 定期更新在线用户列表
        setInterval(() => {
            if (socket && socket.connected) {
                updateOnlineUsers();
            }
        }, 10000); // 每10秒更新一次
    </script>
</body>

</html>