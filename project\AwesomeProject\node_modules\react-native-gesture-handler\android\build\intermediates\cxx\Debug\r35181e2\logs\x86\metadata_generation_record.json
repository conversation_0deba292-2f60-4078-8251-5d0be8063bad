[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: x86", "file_": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\r35181e2\\x86\\android_gradle_build.json due to:", "file_": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"D:\\\\xuexi\\\\JAVA\\\\17\\\\bin\\\\java\" ^\n  --class-path ^\n  \"D:\\\\xuexi\\\\Android Sdk\\\\GradleCache\\\\caches\\\\modules-2\\\\files-2.1\\\\com.google.prefab\\\\cli\\\\2.1.0\\\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\\\cli-2.1.0-all.jar\" ^\n  com.google.prefab.cli.AppKt ^\n  --build-system ^\n  cmake ^\n  --platform ^\n  android ^\n  --abi ^\n  x86 ^\n  --os-version ^\n  24 ^\n  --stl ^\n  c++_shared ^\n  --ndk-version ^\n  27 ^\n  --output ^\n  \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\agp-prefab-staging10962037058449679877\\\\staged-cli-output\" ^\n  \"D:\\\\xuexi\\\\Android Sdk\\\\GradleCache\\\\caches\\\\8.14.1\\\\transforms\\\\5d0a6fceadea979a29a63bd98787fde9\\\\transformed\\\\react-android-0.80.2-debug\\\\prefab\" ^\n  \"D:\\\\ReactNative\\\\A\\\\project\\\\AwesomeProject\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\build\\\\intermediates\\\\cxx\\\\refs\\\\react-native-reanimated\\\\4ow4f674\" ^\n  \"D:\\\\xuexi\\\\Android Sdk\\\\GradleCache\\\\caches\\\\8.14.1\\\\transforms\\\\51dbd9ae21c085b2cb843db84b5d6696\\\\transformed\\\\fbjni-0.7.0\\\\prefab\"\n", "file_": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from 'D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\r35181e2\\x86'", "file_": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder 'D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\r35181e2\\x86'", "file_": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake @echo off\n\"D:\\\\xuexi\\\\Android Sdk\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HD:\\\\ReactNative\\\\A\\\\project\\\\AwesomeProject\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\src\\\\main\\\\jni\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=x86\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=x86\" ^\n  \"-DANDROID_NDK=D:\\\\xuexi\\\\Android Sdk\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_ANDROID_NDK=D:\\\\xuexi\\\\Android Sdk\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=D:\\\\xuexi\\\\Android Sdk\\\\Sdk\\\\ndk\\\\27.1.12297006\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=D:\\\\xuexi\\\\Android Sdk\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_CXX_FLAGS=-O2 -frtti -fexceptions -Wall -Werror -std=c++20 -DANDROID\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\ReactNative\\\\A\\\\project\\\\AwesomeProject\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\r35181e2\\\\obj\\\\x86\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\\\ReactNative\\\\A\\\\project\\\\AwesomeProject\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\r35181e2\\\\obj\\\\x86\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=D:\\\\ReactNative\\\\A\\\\project\\\\AwesomeProject\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\.cxx\\\\Debug\\\\r35181e2\\\\prefab\\\\x86\\\\prefab\" ^\n  \"-BD:\\\\ReactNative\\\\A\\\\project\\\\AwesomeProject\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\.cxx\\\\Debug\\\\r35181e2\\\\x86\" ^\n  -GNinja ^\n  \"-DREACT_NATIVE_DIR=D:\\\\ReactNative\\\\A\\\\project\\\\AwesomeProject\\\\node_modules\\\\react-native\" ^\n  \"-DREACT_NATIVE_MINOR_VERSION=80\" ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\" ^\n  \"-DCMAKE_SHARED_LINKER_FLAGS=-Wl,--build-id=none\"\n", "file_": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"D:\\\\xuexi\\\\Android Sdk\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HD:\\\\ReactNative\\\\A\\\\project\\\\AwesomeProject\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\src\\\\main\\\\jni\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=x86\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=x86\" ^\n  \"-DANDROID_NDK=D:\\\\xuexi\\\\Android Sdk\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_ANDROID_NDK=D:\\\\xuexi\\\\Android Sdk\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=D:\\\\xuexi\\\\Android Sdk\\\\Sdk\\\\ndk\\\\27.1.12297006\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=D:\\\\xuexi\\\\Android Sdk\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_CXX_FLAGS=-O2 -frtti -fexceptions -Wall -Werror -std=c++20 -DANDROID\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\ReactNative\\\\A\\\\project\\\\AwesomeProject\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\r35181e2\\\\obj\\\\x86\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\\\ReactNative\\\\A\\\\project\\\\AwesomeProject\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\r35181e2\\\\obj\\\\x86\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=D:\\\\ReactNative\\\\A\\\\project\\\\AwesomeProject\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\.cxx\\\\Debug\\\\r35181e2\\\\prefab\\\\x86\\\\prefab\" ^\n  \"-BD:\\\\ReactNative\\\\A\\\\project\\\\AwesomeProject\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\.cxx\\\\Debug\\\\r35181e2\\\\x86\" ^\n  -GNinja ^\n  \"-DREACT_NATIVE_DIR=D:\\\\ReactNative\\\\A\\\\project\\\\AwesomeProject\\\\node_modules\\\\react-native\" ^\n  \"-DREACT_NATIVE_MINOR_VERSION=80\" ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\" ^\n  \"-DCMAKE_SHARED_LINKER_FLAGS=-Wl,--build-id=none\"\n", "file_": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Exiting generation of D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\r35181e2\\x86\\compile_commands.json.bin normally", "file_": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "hard linked D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\r35181e2\\x86\\compile_commands.json to D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\.cxx\\tools\\debug\\x86\\compile_commands.json", "file_": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]