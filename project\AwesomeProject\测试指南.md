# 登录注册功能测试指南

## 环境准备

### 1. 安装后端依赖
```bash
cd project/hou
npm install
```

### 2. 安装前端依赖
```bash
cd project/AwesomeProject
npm install
# 如果是iOS，还需要
cd ios && pod install
```

### 3. 启动MongoDB数据库
确保MongoDB数据库正在运行（根据配置文件，使用的是MongoDB Atlas云数据库）

## 测试流程

### 1. 启动后端服务
```bash
cd project/hou
npm start
```
服务器将在 http://localhost:3000 启动

### 2. 启动移动端应用
```bash
cd project/AwesomeProject
npm start
# 在另一个终端运行
npm run android  # 或 npm run ios
```

### 3. 测试注册功能
1. 打开应用，应该看到登录页面
2. 点击"立即注册"按钮跳转到注册页面
3. 填写注册信息：
   - 用户名：test123（必填，至少3位）
   - 密码：test123456（必填，至少6位，包含字母和数字）
   - 确认密码：test123456（必须与密码一致）
   - 邮箱：<EMAIL>（可选）
   - 手机号：13800138000（可选）
4. 点击"注册"按钮
5. 注册成功后会弹出提示，点击确定跳转到登录页面

### 4. 测试登录功能
1. 在登录页面输入刚注册的用户名和密码
2. 点击"登录"按钮
3. 登录成功后会弹出提示，点击确定进入主应用

### 5. 测试状态管理
1. 登录成功后，应用会保存用户状态
2. 关闭应用重新打开，应该自动保持登录状态
3. 可以在"我的"页面查看用户信息

## API接口测试

### 注册接口
```
POST http://localhost:3000/users/register
Content-Type: application/json

{
  "username": "test123",
  "password": "test123456",
  "email": "<EMAIL>",
  "phone": "13800138000"
}
```

### 登录接口
```
POST http://localhost:3000/users/login
Content-Type: application/json

{
  "username": "test123",
  "password": "test123456"
}
```

### 获取用户信息接口
```
GET http://localhost:3000/users/profile
Authorization: Bearer <your-jwt-token>
```

## 功能特性

### 前端特性
- ✅ 响应式UI设计
- ✅ 表单验证和错误提示
- ✅ 加载状态显示
- ✅ 用户状态管理（Context + AsyncStorage）
- ✅ 自动登录保持
- ✅ 导航路由配置

### 后端特性
- ✅ 用户注册和登录
- ✅ 密码加密（bcrypt）
- ✅ JWT token验证
- ✅ 数据验证和错误处理
- ✅ MongoDB数据存储
- ✅ CORS支持

## 注意事项

1. **网络配置**：确保移动端设备/模拟器能访问后端服务（http://localhost:3000）
2. **数据库连接**：确保MongoDB连接配置正确
3. **依赖安装**：确保所有新添加的依赖都已安装
4. **权限配置**：iOS可能需要额外的网络权限配置

## 常见问题

1. **网络请求失败**：检查设备网络和后端服务是否启动
2. **数据库连接失败**：检查MongoDB配置和网络连接
3. **依赖缺失**：重新安装npm依赖
4. **Android构建问题**：清理Gradle缓存，确保JDK版本正确

## 完成状态

✅ 所有登录注册功能已完成并可测试
✅ 前后端完整集成
✅ 用户状态管理完善
✅ API接口完整实现