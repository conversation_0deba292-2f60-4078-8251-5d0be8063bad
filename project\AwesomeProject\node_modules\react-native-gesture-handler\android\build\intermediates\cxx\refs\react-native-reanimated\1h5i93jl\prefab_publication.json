{"installationFolder": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\prefab_package\\debug\\prefab", "gradlePath": ":react-native-reanimated", "packageInfo": {"packageName": "react-native-reanimated", "packageVersion": "4.0.1", "packageSchemaVersion": 2, "packageDependencies": [], "modules": [{"moduleName": "reanimated", "moduleHeaders": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-reanimated\\android\\build\\prefab-headers\\reanimated", "moduleExportLibraries": [], "abis": [{"abiName": "x86_64", "abiApi": 24, "abiNdkMajor": 27, "abiStl": "c++_shared", "abiLibrary": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\4h2q6r21\\obj\\x86_64\\libreanimated.so", "abiAndroidGradleBuildJsonFile": "D:\\ReactNative\\A\\project\\AwesomeProject\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\4h2q6r21\\x86_64\\android_gradle_build.json"}]}]}}