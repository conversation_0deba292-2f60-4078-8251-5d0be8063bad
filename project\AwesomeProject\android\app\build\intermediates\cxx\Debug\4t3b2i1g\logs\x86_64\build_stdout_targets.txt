ninja: Entering directory `D:\ReactNative\A\project\AwesomeProject\android\app\.cxx\Debug\4t3b2i1g\x86_64'
[0/2] Re-checking globbed directories...
[1/67] Building CXX object CMakeFiles/appmodules.dir/D_/ReactNative/A/project/AwesomeProject/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[2/67] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[3/67] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o
[4/67] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o
[5/67] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o
[6/67] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o
[7/67] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o
[8/67] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o
[9/67] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o
[10/67] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o
[11/67] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp.o
[12/67] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o
[13/67] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o
[14/67] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp.o
[15/67] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o
[16/67] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o
[17/67] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o
[18/67] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o
[19/67] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o
[20/67] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o
[21/67] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o
[22/67] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o
[23/67] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o
[24/67] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/fb11c82c140a16fe66607aba37bca469/renderer/components/safeareacontext/EventEmitters.cpp.o
[25/67] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/36cc7e4f7c143e028a6fb288618cc92c/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o
[26/67] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/36cc7e4f7c143e028a6fb288618cc92c/components/safeareacontext/RNCSafeAreaViewState.cpp.o
[27/67] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7ab3dbfc414b53a74a6fbcd6c6024202/components/safeareacontext/ComponentDescriptors.cpp.o
[28/67] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3102b4cb73674bba3518bf6e7ebbd7ca/jni/react/renderer/components/safeareacontext/Props.cpp.o
[29/67] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/3102b4cb73674bba3518bf6e7ebbd7ca/jni/react/renderer/components/safeareacontext/States.cpp.o
[30/67] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/5471374b40316f0ce24d4ce738b43268/source/codegen/jni/safeareacontext-generated.cpp.o
[31/67] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/fd44bf2af17c9885e2b06855f1a8570a/safeareacontext/safeareacontextJSI-generated.cpp.o
[32/67] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/fb11c82c140a16fe66607aba37bca469/renderer/components/safeareacontext/ShadowNodes.cpp.o
[33/67] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/7170110d2aaf1821d5c8a5f63553fd5f/cpp/react/renderer/components/rnscreens/RNSBottomTabsState.cpp.o
[34/67] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/4eaf070e219489ee5eaa04a54f5f8a28/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o
[35/67] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/7170110d2aaf1821d5c8a5f63553fd5f/cpp/react/renderer/components/rnscreens/RNSBottomTabsShadowNode.cpp.o
[36/67] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/7170110d2aaf1821d5c8a5f63553fd5f/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o
[37/67] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/7170110d2aaf1821d5c8a5f63553fd5f/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o
[38/67] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/4eaf070e219489ee5eaa04a54f5f8a28/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o
[39/67] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/21c2ed0500c30c63a9d93443faf63505/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o
[40/67] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/4eaf070e219489ee5eaa04a54f5f8a28/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o
[41/67] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/21c2ed0500c30c63a9d93443faf63505/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o
[42/67] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b7575ff1969891f9a2f254655683fb9d/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o
[43/67] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/239ae99fed4a43719bf832c3deeb3aa8/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o
[44/67] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b8c09388ff72cfeaefa92c766539e848/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o
[45/67] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o
[46/67] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/1417aff1ef66954b0563e2d55a615f9d/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o
[47/67] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/1e007443f7b23950854c7b4c739c763b/react/renderer/components/rnscreens/RNSSplitViewScreenShadowNode.cpp.o
[48/67] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b8c09388ff72cfeaefa92c766539e848/source/codegen/jni/react/renderer/components/rnscreens/States.cpp.o
[49/67] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/239ae99fed4a43719bf832c3deeb3aa8/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o
[50/67] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/1417aff1ef66954b0563e2d55a615f9d/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o
[51/67] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o
[52/67] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o
[53/67] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o
[54/67] Linking CXX shared library D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_safeareacontext.so
[55/67] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o
[56/67] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o
[57/67] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o
[58/67] Building CXX object RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o
[59/67] Building CXX object rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/ComponentDescriptors.cpp.o
[60/67] Building CXX object rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/rnworklets-generated.cpp.o
[61/67] Building CXX object rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/States.cpp.o
[62/67] Building CXX object rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/Props.cpp.o
[63/67] Building CXX object rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/rnworkletsJSI-generated.cpp.o
[64/67] Linking CXX shared library D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libreact_codegen_rnscreens.so
[65/67] Building CXX object rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/EventEmitters.cpp.o
[66/67] Building CXX object rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/ShadowNodes.cpp.o
[67/67] Linking CXX shared library D:\ReactNative\A\project\AwesomeProject\android\app\build\intermediates\cxx\Debug\4t3b2i1g\obj\x86_64\libappmodules.so
