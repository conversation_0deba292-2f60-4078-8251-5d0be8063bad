# Values used to calculate the hash in this folder name.
# Should not depend on the absolute path of the project itself.
#   - AGP: 8.9.2.
#   - $NDK is the path to NDK 27.1.12297006.
#   - $PROJECT is the path to the parent folder of the root Gradle build file.
#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.
#   - $HASH is the hash value computed from this text.
#   - $CMAKE is the path to CMake 3.22.1.
#   - $NINJA is the path to Ninja.
-HD:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/src/main/jni
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=$ABI
-DCMAKE_ANDROID_ARCH_ABI=$ABI
-DANDROID_NDK=$NDK
-DCMAKE_ANDROID_NDK=$NDK
-DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake
-DCMA<PERSON>_MAKE_PROGRAM=$NINJA
-DCMAKE_CXX_FLAGS=-O2 -frtti -fexceptions -Wall -Werror -std=c++20 -DANDROID
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/intermediates/cxx/Debug/$HASH/obj/$ABI
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/build/intermediates/cxx/Debug/$HASH/obj/$ABI
-DCMAKE_BUILD_TYPE=Debug
-DCMAKE_FIND_ROOT_PATH=D:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/.cxx/Debug/$HASH/prefab/$ABI/prefab
-BD:/ReactNative/A/project/AwesomeProject/node_modules/react-native-gesture-handler/android/.cxx/Debug/$HASH/$ABI
-GNinja
-DREACT_NATIVE_DIR=D:/ReactNative/A/project/AwesomeProject/node_modules/react-native
-DREACT_NATIVE_MINOR_VERSION=80
-DANDROID_STL=c++_shared
-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON
-DCMAKE_SHARED_LINKER_FLAGS=-Wl,--build-id=none